#ifndef _FABRIC_H_
#define _FABRIC_H_

#include <sys/eventfd.h>
#include <inttypes.h>
#include <time.h>
#include <pthread.h>
#include <arpa/inet.h>
#include <stdatomic.h>
#include <ifaddrs.h>
#include "fab_config.h"
#include "uthash.h"
#include "thpool.h"
#include "memory_pool.h"

#include <sys/socket.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <numa.h>
#include <numaif.h>
#include <unistd.h>

#include <rpc/types.h>
#include <rpc/xdr.h>
#include <rpc/xdr_ioq.h>
#include <rpc/rpc.h>
#include <rpc/svc_rqst.h>
#include <rpc/svc_auth.h>

#include "misc/city.h"
#include "misc/portable.h"
#include "misc/abstract_atomic.h"

#include "../time_utils.h"

#define CTDB_IP_COMMAND "ctdb ip"
#define MAX_VIP 2048
#define MAX_DEV 16
#define DEVSIZE 256

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_IOVECS 32

enum ep_state {
    EP_STATE_INIT,
    EP_STATE_CONNECTED,
    EP_STATE_CLOSED,
};

enum callback_error_code {
	CALLBACK_SUCEESS = 0,
	CALLBACK_FAILED = -1,    
	CALLBACK_ERROR_WAITDESTORY_LIMIT = -2,
	CALLBACK_ERROR_NOCONNECTED = -3,
	CALLBACK_ERROR_FI_SENDV_FAILED = -4,
	CALLBACK_ERROR_FI_READV_NOMEM = -5,
	CALLBACK_ERROR_FI_READV_FAILED = -6,
	CALLBACK_ERROR_FI_READ_FAILED = -7,
	CALLBACK_ERROR_FI_WRITE_FAILED = -8,
	CALLBACK_ERROR_FI_WRITEV_FAILED = -9,
};

// fabric endpoint
typedef struct fabric_endpoint {
	struct fi_info *fi;
	struct fid_ep *ep;
	enum ep_state state;
	struct fabric_domain *domain;
	//gnfs xd
	RDMAXPRT *xd;
	//hash handle
	UT_hash_handle hh;

	//内存池
	memory_pool *mpool;
	struct fid_mr *pool_mr;

	//gnfs 内存mr test
	memory_pool *gane_mpool;
	struct fid_mr *gane_mr;

	struct sockaddr_storage peer_addr;
	struct sockaddr_storage local_addr;
	int thrid;
	uint64_t ref_count;
	uint64_t credits;
	uint64_t send_wait_cq_count;
	uint64_t write_wait_cq_count;
	uint32_t session_id;
	struct timespec online_time;
	bool active;
	uint64_t fi_send_count;
	uint64_t fi_write_count;
	uint64_t fi_read_count;
	uint64_t fi_recv_count;
	uint64_t fi_send_cqcallback_success_count;
	uint64_t fi_write_cqcallback_success_count;
	uint64_t fi_read_cqcallback_success_count;
	uint64_t fi_recv_cqcallback_success_count;
	uint64_t fi_send_cqcallback_failed_count;
	uint64_t fi_write_cqcallback_failed_count;
	uint64_t fi_read_cqcallback_failed_count;
	uint64_t fi_recv_cqcallback_failed_count;
	uint64_t in_process_count;
} fabric_endpoint;

struct fabric_domain {
	struct fabric_class *fab_clas;
	struct fid_domain *fi_domain;
	int index;
	//verbs 不支持
	//struct fid_poll *poll_set;

	//cq
	struct fid_cq **tx_cq;
	struct fid_cq **rx_cq;

	//verbs 不支持
	//可扩展端点，适用于多对多通信
	//struct fid_av *av;
	//struct fid_ep *sep;

	//管理多个ep
	//hash/
	pthread_mutex_t map_lock;
	fabric_endpoint *ep_map;
	fabric_endpoint *ep_destory_map;

	//内存池
	memory_pool *mpool;
	struct fid_mr *pool_mr;

	//gnfs 内存mr test
	memory_pool *gane_mpool;
	struct fid_mr *gane_mr;

	//线程
	pthread_t poll_cq_thread;

	//
	char netdev[DEVSIZE];
	char ip_list[MAX_VIP][INET6_ADDRSTRLEN];
	int ip_count;
};

struct fabric_eps_list {
	//pthread_mutex_t map_lock;
	//UT_hash_handle hh;
	int ep_count;
};

struct fabric_pep {
	struct fabric_pep *next;
	struct sockaddr *ifa_addr;
	struct fid_pep *pep;
	char ip[INET6_ADDRSTRLEN];
	struct fabric_domain *domain;
};


// fabric class
typedef struct fabric_class {
	struct config *conf;
	struct fi_info *fi;
	struct fi_info *effect_fi;
	struct fid_fabric *fabric;
	struct fid_eq *eq;
	struct fabric_domain *domains[MAX_DEV];
	int domain_count;
	struct fid_wait *wait_set;
	struct fabric_pep *fab_pep;
	bool thr_nolock;
	bool thr_work_send;
	threadpool rx_thpool;
	threadpool tx_thpool;
	threadpool work_thpool;
	threadpool cq_thpool;
	threadpool rpc_thpool;
	threadpool qos_thpool;
	struct rpc_rdma_attr *attr;
	struct fabric_eps_list *eps_list;
//	void *attr;
	struct ifaddrs *ifaddr_list;
	pthread_mutex_t pep_lock;  /* pep 链表互斥锁 */
	pthread_mutex_t domain_lock;
	volatile uint64_t stop;  /* 线程stop标志 */
} fabric_class;

//nfs
/*回调函数*/
typedef int (*fabric_rpc_callback)(void *, void *, struct iovec *, int, int);

//一般上下文， recv/read/write
struct general_context {
    struct fabric_endpoint *ep;
    struct iovec *iov;
    int cnt;
    void *cb_arg;
    fabric_rpc_callback cb;
    int return_code;
    struct timespec recv_cq_time;
    struct timespec start_sendv_handle_cb_time;
    struct timespec start_writev_handle_cb_time;
    struct timespec start_sendv_handle_time;
    struct timespec start_writev_handle_time;
    struct timespec start_sendv_cq_time;
    struct timespec start_writev_cq_time;
    struct timespec start_readv_cq_cb_time;
    struct timespec start_writev_cq_cb_time;
    struct timespec start_sendv_cq_cb_time;

    uint64_t flags;
    size_t len;
    void* chunk_desc[MAX_IOVECS];
    struct fi_rma_iov rma_iov[MAX_IOVECS];
    int rma_num;
};

//gnfs memory
char *alloc_registed_memory(long page_size, uint64_t size);
int get_netdev_by_addr(const char* node, char* interface);

//init
//fabric_class *fabric_class_alloc(const struct rpc_rdma_attr *attr);
fabric_class *fabric_class_alloc(struct rpc_rdma_attr *attr);
fabric_class *fabric_init(char* node, int service, const struct rpc_rdma_attr *attr, RDMAXPRT *xd);
int fabric_free(fabric_class* fab_clas);
//ep
int fabric_enable_ep(fabric_class *fabric_clas, fabric_endpoint *fabric_ep);
fabric_endpoint *fabric_open_ep(fabric_class *fab_clas, struct fi_info *info);
fabric_endpoint *fabric_open_endpoint(fabric_class *fab_clas, const char *addr, int port);
int fabric_connect(fabric_endpoint *fab_endp);
int destory_all_endpoint(struct fabric_domain *domain);
//data transfer
int fabric_recv(fabric_endpoint *fab_endp, int num);
int fabric_ep_prepare_recv(fabric_endpoint *fab_endp);
int fabric_ep_release_chunk(struct fabric_endpoint *ep, struct iovec *iov, int iov_cnt);
//work thread
int fabric_workpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx);
int fabric_rpcpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx);
/*
*fd: 链接的句柄
*iovcnt 个数
*iov 要写的数据
*callback_arg： 回调回传参数
*callback_func：回调函数
*/
int fabric_sendv(void *fd, int iov_cnt, struct iovec *iov, void *callback_arg, fabric_rpc_callback callback_func);

/*
*fd: 链接的句柄
*readlist：地址列表 
*callback_arg： 回调回传参数
*callback_func：回调函数
*/
int fabric_readv(void *fd, struct xdr_read_list *readlist, void *callback_arg, fabric_rpc_callback callback_func);

/*
*fd: 链接的句柄
*readlist：地址列表 
*iovcnt 个数
*iov 要写的数据
*callback_arg： 回调回传参数
*callback_func：回调函数
*/
int fabric_writev(void *fd, struct xdr_write_list *writelist, int iov_cnt, struct iovec *iov, void *callback_arg, fabric_rpc_callback callback_func, int list_size);
void fabric_shutdown(fabric_endpoint *fab_ep);
//pep
int fabric_open_pep_listen(fabric_class *fab_clas, struct fi_info *fi, const char *ip, struct fabric_domain *domain);
int fabric_listen(struct fabric_pep *fab_pep);
int fabric_accept(fabric_endpoint **fab_endp, fabric_class *fab_clas, struct fi_info *info);
//poll
int fabric_eq_readerr(struct fid_eq *eq);
void handle_callback(void *context);
int handle_cq_event(fabric_class *fab_clas, struct fid_cq *cq, int is_tx);
int handle_eq_event(fabric_class *fab_clas, struct fid_eq *eq);
int put_cq_event(fabric_class* fabric);
void *fabric_event_process(void *fabric);
int fabric_poll_start(fabric_class *fab_clas);
int fabric_poll_stop(struct fabric_domain *domain);
//domian
int destory_domain(struct fabric_domain *domain);
void destory_fab_ep(fabric_endpoint *fe);
#define MSG_PRINTERR(call, retv)                                        \
    do {						                \
        int saved_errno = errno;			                \
        fprintf(stderr, call "(): %s:%d, ret=%d (%s)\n",                \
                __FILE__, __LINE__, (int) (retv),		        \
                fi_strerror((int) -(retv)));			        \
        errno = saved_errno;			                        \
    } while (0)

#define FT_LOG(level, fmt, ...)						\
	do {								\
		int saved_errno = errno;				\
		fprintf(stderr, "[%s] fabtests:%s:%d: " fmt "\n",	\
			level, __FILE__, __LINE__, ##__VA_ARGS__);	\
		errno = saved_errno;					\
	} while (0)

#define FT_ERR(fmt, ...) FT_LOG("error", fmt, ##__VA_ARGS__)

#define FT_CQ_ERR(cq, entry, buf, len)					\
	FT_ERR("cq_readerr %d (%s), provider errno: %d (%s)",		\
		entry.err, fi_strerror(entry.err),			\
		entry.prov_errno, fi_cq_strerror(cq, entry.prov_errno,	\
						 entry.err_data,	\
						 buf, len))		\

int fabric_txpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx);
int fabric_cqpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx, int dest_thrid);
int fabric_rxpool_submit(fabric_class *fab_clas, void (*handle_callback)(void *), void *ctx);
extern struct fabric_class *fab_clas;

int fabric_add_virtual_ip(const char *vip);
int fabric_delete_virtual_ip(const char *vip);
void fabric_get_listen_ips(char **ips, int max_nums, int *ip_nums);

struct fi_info *get_fi_by_ip(const char *ip, const char *port, struct fi_info *hints);
char *get_ip_by_fi(struct fi_info *fi);
int add_ip_to_domain(const char *ip, struct fabric_domain *domain);
int delete_ip_from_domain(fabric_class *fab_clas, const char *ip, struct fabric_domain *domain);

struct fabric_domain *get_domain_by_ip(fabric_class *fab_clas, const char *ip);
struct fabric_domain *get_domain_by_fi(fabric_class *fab_clas, struct fi_info *fi);

int fabric_creat_domain_and_start_listen(fabric_class *fab_clas, const char *ip, RDMAXPRT *xd);

struct fabric_domain *fabric_open_domain(fabric_class *fab_clas, struct fi_info *fi_for_domain, RDMAXPRT *xd);

void destory_map_process(fabric_class *fab_clas, struct fabric_domain *domain, int thread_id);

#ifdef __cplusplus
}
#endif

#endif /* _FABRIC_H_ */
