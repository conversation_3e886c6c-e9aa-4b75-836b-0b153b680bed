/* A Bison parser, made by GNU Bison 3.0.4.  */

/* Bison implementation for Yacc-like parsers in C

   Copyright (C) 1984, 1989-1990, 2000-2015 Free Software Foundation, Inc.

   This program is free software: you can redistribute it and/or modify
   it under the terms of the GNU General Public License as published by
   the Free Software Foundation, either version 3 of the License, or
   (at your option) any later version.

   This program is distributed in the hope that it will be useful,
   but WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
   GNU General Public License for more details.

   You should have received a copy of the GNU General Public License
   along with this program.  If not, see <http://www.gnu.org/licenses/>.  */

/* As a special exception, you may create a larger work that contains
   part or all of the Bison parser skeleton and distribute that work
   under terms of your choice, so long as that work isn't itself a
   parser generator using the skeleton or a modified version thereof
   as a parser skeleton.  Alternatively, if you modify or redistribute
   the parser skeleton itself, you may (at your option) remove this
   special exception, which will cause the skeleton and the resulting
   Bison output files to be licensed under the GNU General Public
   License without this special exception.

   This special exception was added by the Free Software Foundation in
   version 2.2 of Bison.  */

/* C LALR(1) parser skeleton written by <PERSON>, by
   simplifying the original so-called "semantic" parser.  */

/* All symbols defined below should begin with yy or YY, to avoid
   infringing on user name space.  This should be done even for local
   variables, as they might otherwise be expanded by user macros.
   There are some unavoidable exceptions within include files to
   define necessary library symbols; they are noted "INFRINGES ON
   USER NAME SPACE" below.  */

/* Identify Bison output.  */
#define YYBISON 1

/* Bison version.  */
#define YYBISON_VERSION "3.0.4"

/* Skeleton name.  */
#define YYSKELETON_NAME "yacc.c"

/* Pure parsers.  */
#define YYPURE 1

/* Push parsers.  */
#define YYPUSH 0

/* Pull parsers.  */
#define YYPULL 1

/* "%code top" blocks.  */
#line 1 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:316  */


#pragma GCC diagnostic ignored "-Wunused-value"
#pragma GCC diagnostic ignored "-Wunused-variable"

#include "config.h"
#include "config_parsing.h"
#include "analyse.h"
#include "abstract_mem.h"

#include <stdio.h>
#include "log.h"

#if HAVE_STRING_H
#   include <string.h>
#endif


#line 82 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:316  */

/* Substitute the variable and function names.  */
#define yyparse         gnfs_yyparse
#define yylex           gnfs_yylex
#define yyerror         gnfs_yyerror
#define yydebug         gnfs_yydebug
#define yynerrs         gnfs_yynerrs


/* Copy the first part of user declarations.  */

#line 94 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:339  */

# ifndef YY_NULLPTR
#  if defined __cplusplus && 201103L <= __cplusplus
#   define YY_NULLPTR nullptr
#  else
#   define YY_NULLPTR 0
#  endif
# endif

/* Enabling verbose error messages.  */
#ifdef YYERROR_VERBOSE
# undef YYERROR_VERBOSE
# define YYERROR_VERBOSE 1
#else
# define YYERROR_VERBOSE 0
#endif

/* In a future release of Bison, this section will be replaced
   by #include "conf_yacc.h".  */
#ifndef YY_GNFS_YY_ROOT_RPMBUILD_BUILD_GNFS_4_0_7_1_10_20_CONFIG_PARSING_CONF_YACC_H_INCLUDED
# define YY_GNFS_YY_ROOT_RPMBUILD_BUILD_GNFS_4_0_7_1_10_20_CONFIG_PARSING_CONF_YACC_H_INCLUDED
/* Debug traces.  */
#ifndef YYDEBUG
# define YYDEBUG 0
#endif
#if YYDEBUG
extern int gnfs_yydebug;
#endif
/* "%code requires" blocks.  */
#line 26 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:355  */

/* alert the parser that we have our own definition */
# define YYLTYPE_IS_DECLARED 1


#line 130 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:355  */

/* Token type.  */
#ifndef YYTOKENTYPE
# define YYTOKENTYPE
  enum yytokentype
  {
    _ERROR_ = 258,
    LCURLY_OP = 259,
    RCURLY_OP = 260,
    EQUAL_OP = 261,
    COMMA_OP = 262,
    SEMI_OP = 263,
    IDENTIFIER = 264,
    STRING = 265,
    DQUOTE = 266,
    SQUOTE = 267,
    TOKEN = 268,
    REGEX_TOKEN = 269,
    TOK_PATH = 270,
    TOK_TRUE = 271,
    TOK_FALSE = 272,
    TOK_DECNUM = 273,
    TOK_HEXNUM = 274,
    TOK_OCTNUM = 275,
    TOK_ARITH_OP = 276,
    TOK_V4_ANY = 277,
    TOK_V4ADDR = 278,
    TOK_V4CIDR = 279,
    TOK_V6ADDR = 280,
    TOK_V6CIDR = 281,
    TOK_FSID = 282,
    TOK_NETGROUP = 283
  };
#endif

/* Value type.  */
#if ! defined YYSTYPE && ! defined YYSTYPE_IS_DECLARED

union YYSTYPE
{
#line 32 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:355  */

  char *token;
  struct config_node *node;

#line 176 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:355  */
};

typedef union YYSTYPE YYSTYPE;
# define YYSTYPE_IS_TRIVIAL 1
# define YYSTYPE_IS_DECLARED 1
#endif

/* Location type.  */
#if ! defined YYLTYPE && ! defined YYLTYPE_IS_DECLARED
typedef struct YYLTYPE YYLTYPE;
struct YYLTYPE
{
  int first_line;
  int first_column;
  int last_line;
  int last_column;
};
# define YYLTYPE_IS_DECLARED 1
# define YYLTYPE_IS_TRIVIAL 1
#endif



int gnfs_yyparse (struct parser_state *st);
/* "%code provides" blocks.  */
#line 37 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:355  */


typedef struct YYLTYPE {
  int first_line;
  int first_column;
  int last_line;
  int last_column;
  char *filename;
} YYLTYPE;

# define YYLLOC_DEFAULT(Current, Rhs, N)			       \
    do								       \
      if (N)							       \
	{							       \
	  (Current).first_line	 = YYRHSLOC (Rhs, 1).first_line;       \
	  (Current).first_column = YYRHSLOC (Rhs, 1).first_column;     \
	  (Current).last_line	 = YYRHSLOC (Rhs, N).last_line;	       \
	  (Current).last_column	 = YYRHSLOC (Rhs, N).last_column;      \
	  (Current).filename	 = YYRHSLOC (Rhs, 1).filename;	       \
	}							       \
      else							       \
	{ /* empty RHS */					       \
	  (Current).first_line	 = (Current).last_line	 =	       \
	    YYRHSLOC (Rhs, 0).last_line;			       \
	  (Current).first_column = (Current).last_column =	       \
	    YYRHSLOC (Rhs, 0).last_column;			       \
	  (Current).filename  = NULL;			     /* new */ \
	}							       \
    while (0)

int ganeshun_yylex(YYSTYPE *yylval_param,
		   YYLTYPE *yylloc_param,
		   void *scanner);

int gnfs_yylex(YYSTYPE *yylval_param,
		  YYLTYPE *yylloc_param,
		  struct parser_state *st);

void config_parse_error(YYLTYPE *yylloc_param,
			struct parser_state *st,
			char *format, ...);

void gnfs_yyerror(YYLTYPE *yylloc_param,
		     void *yyscanner,
		     char*);

extern struct glist_head all_blocks;

struct config_node *config_block(char *blockname,
				 struct config_node *list,
				 YYLTYPE *yylloc_param,
				 struct parser_state *st);

void link_node(struct config_node *node);

 struct config_node *link_sibling(struct config_node *first,
				  struct config_node *second);

struct config_node *config_stmt(char *varname,
				struct config_node *exprlist,
				 YYLTYPE *yylloc_param,
				struct parser_state *st);

struct config_node *config_term(char *opcode,
				char *varval,
				enum term_type type,
				 YYLTYPE *yylloc_param,
				struct parser_state *st);


#line 273 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:355  */

#endif /* !YY_GNFS_YY_ROOT_RPMBUILD_BUILD_GNFS_4_0_7_1_10_20_CONFIG_PARSING_CONF_YACC_H_INCLUDED  */

/* Copy the second part of user declarations.  */

#line 279 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:358  */

#ifdef short
# undef short
#endif

#ifdef YYTYPE_UINT8
typedef YYTYPE_UINT8 yytype_uint8;
#else
typedef unsigned char yytype_uint8;
#endif

#ifdef YYTYPE_INT8
typedef YYTYPE_INT8 yytype_int8;
#else
typedef signed char yytype_int8;
#endif

#ifdef YYTYPE_UINT16
typedef YYTYPE_UINT16 yytype_uint16;
#else
typedef unsigned short int yytype_uint16;
#endif

#ifdef YYTYPE_INT16
typedef YYTYPE_INT16 yytype_int16;
#else
typedef short int yytype_int16;
#endif

#ifndef YYSIZE_T
# ifdef __SIZE_TYPE__
#  define YYSIZE_T __SIZE_TYPE__
# elif defined size_t
#  define YYSIZE_T size_t
# elif ! defined YYSIZE_T
#  include <stddef.h> /* INFRINGES ON USER NAME SPACE */
#  define YYSIZE_T size_t
# else
#  define YYSIZE_T unsigned int
# endif
#endif

#define YYSIZE_MAXIMUM ((YYSIZE_T) -1)

#ifndef YY_
# if defined YYENABLE_NLS && YYENABLE_NLS
#  if ENABLE_NLS
#   include <libintl.h> /* INFRINGES ON USER NAME SPACE */
#   define YY_(Msgid) dgettext ("bison-runtime", Msgid)
#  endif
# endif
# ifndef YY_
#  define YY_(Msgid) Msgid
# endif
#endif

#ifndef YY_ATTRIBUTE
# if (defined __GNUC__                                               \
      && (2 < __GNUC__ || (__GNUC__ == 2 && 96 <= __GNUC_MINOR__)))  \
     || defined __SUNPRO_C && 0x5110 <= __SUNPRO_C
#  define YY_ATTRIBUTE(Spec) __attribute__(Spec)
# else
#  define YY_ATTRIBUTE(Spec) /* empty */
# endif
#endif

#ifndef YY_ATTRIBUTE_PURE
# define YY_ATTRIBUTE_PURE   YY_ATTRIBUTE ((__pure__))
#endif

#ifndef YY_ATTRIBUTE_UNUSED
# define YY_ATTRIBUTE_UNUSED YY_ATTRIBUTE ((__unused__))
#endif

#if !defined _Noreturn \
     && (!defined __STDC_VERSION__ || __STDC_VERSION__ < 201112)
# if defined _MSC_VER && 1200 <= _MSC_VER
#  define _Noreturn __declspec (noreturn)
# else
#  define _Noreturn YY_ATTRIBUTE ((__noreturn__))
# endif
#endif

/* Suppress unused-variable warnings by "using" E.  */
#if ! defined lint || defined __GNUC__
# define YYUSE(E) ((void) (E))
#else
# define YYUSE(E) /* empty */
#endif

#if defined __GNUC__ && 407 <= __GNUC__ * 100 + __GNUC_MINOR__
/* Suppress an incorrect diagnostic about yylval being uninitialized.  */
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN \
    _Pragma ("GCC diagnostic push") \
    _Pragma ("GCC diagnostic ignored \"-Wuninitialized\"")\
    _Pragma ("GCC diagnostic ignored \"-Wmaybe-uninitialized\"")
# define YY_IGNORE_MAYBE_UNINITIALIZED_END \
    _Pragma ("GCC diagnostic pop")
#else
# define YY_INITIAL_VALUE(Value) Value
#endif
#ifndef YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
# define YY_IGNORE_MAYBE_UNINITIALIZED_END
#endif
#ifndef YY_INITIAL_VALUE
# define YY_INITIAL_VALUE(Value) /* Nothing. */
#endif


#if ! defined yyoverflow || YYERROR_VERBOSE

/* The parser invokes alloca or malloc; define the necessary symbols.  */

# ifdef YYSTACK_USE_ALLOCA
#  if YYSTACK_USE_ALLOCA
#   ifdef __GNUC__
#    define YYSTACK_ALLOC __builtin_alloca
#   elif defined __BUILTIN_VA_ARG_INCR
#    include <alloca.h> /* INFRINGES ON USER NAME SPACE */
#   elif defined _AIX
#    define YYSTACK_ALLOC __alloca
#   elif defined _MSC_VER
#    include <malloc.h> /* INFRINGES ON USER NAME SPACE */
#    define alloca _alloca
#   else
#    define YYSTACK_ALLOC alloca
#    if ! defined _ALLOCA_H && ! defined EXIT_SUCCESS
#     include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
      /* Use EXIT_SUCCESS as a witness for stdlib.h.  */
#     ifndef EXIT_SUCCESS
#      define EXIT_SUCCESS 0
#     endif
#    endif
#   endif
#  endif
# endif

# ifdef YYSTACK_ALLOC
   /* Pacify GCC's 'empty if-body' warning.  */
#  define YYSTACK_FREE(Ptr) do { /* empty */; } while (0)
#  ifndef YYSTACK_ALLOC_MAXIMUM
    /* The OS might guarantee only one guard page at the bottom of the stack,
       and a page size can be as small as 4096 bytes.  So we cannot safely
       invoke alloca (N) if N exceeds 4096.  Use a slightly smaller number
       to allow for a few compiler-allocated temporary stack slots.  */
#   define YYSTACK_ALLOC_MAXIMUM 4032 /* reasonable circa 2006 */
#  endif
# else
#  define YYSTACK_ALLOC YYMALLOC
#  define YYSTACK_FREE YYFREE
#  ifndef YYSTACK_ALLOC_MAXIMUM
#   define YYSTACK_ALLOC_MAXIMUM YYSIZE_MAXIMUM
#  endif
#  if (defined __cplusplus && ! defined EXIT_SUCCESS \
       && ! ((defined YYMALLOC || defined malloc) \
             && (defined YYFREE || defined free)))
#   include <stdlib.h> /* INFRINGES ON USER NAME SPACE */
#   ifndef EXIT_SUCCESS
#    define EXIT_SUCCESS 0
#   endif
#  endif
#  ifndef YYMALLOC
#   define YYMALLOC malloc
#   if ! defined malloc && ! defined EXIT_SUCCESS
void *malloc (YYSIZE_T); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
#  ifndef YYFREE
#   define YYFREE free
#   if ! defined free && ! defined EXIT_SUCCESS
void free (void *); /* INFRINGES ON USER NAME SPACE */
#   endif
#  endif
# endif
#endif /* ! defined yyoverflow || YYERROR_VERBOSE */


#if (! defined yyoverflow \
     && (! defined __cplusplus \
         || (defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL \
             && defined YYSTYPE_IS_TRIVIAL && YYSTYPE_IS_TRIVIAL)))

/* A type that is properly aligned for any stack member.  */
union yyalloc
{
  yytype_int16 yyss_alloc;
  YYSTYPE yyvs_alloc;
  YYLTYPE yyls_alloc;
};

/* The size of the maximum gap between one aligned stack and the next.  */
# define YYSTACK_GAP_MAXIMUM (sizeof (union yyalloc) - 1)

/* The size of an array large to enough to hold all stacks, each with
   N elements.  */
# define YYSTACK_BYTES(N) \
     ((N) * (sizeof (yytype_int16) + sizeof (YYSTYPE) + sizeof (YYLTYPE)) \
      + 2 * YYSTACK_GAP_MAXIMUM)

# define YYCOPY_NEEDED 1

/* Relocate STACK from its old location to the new one.  The
   local variables YYSIZE and YYSTACKSIZE give the old and new number of
   elements in the stack, and YYPTR gives the new location of the
   stack.  Advance YYPTR to a properly aligned location for the next
   stack.  */
# define YYSTACK_RELOCATE(Stack_alloc, Stack)                           \
    do                                                                  \
      {                                                                 \
        YYSIZE_T yynewbytes;                                            \
        YYCOPY (&yyptr->Stack_alloc, Stack, yysize);                    \
        Stack = &yyptr->Stack_alloc;                                    \
        yynewbytes = yystacksize * sizeof (*Stack) + YYSTACK_GAP_MAXIMUM; \
        yyptr += yynewbytes / sizeof (*yyptr);                          \
      }                                                                 \
    while (0)

#endif

#if defined YYCOPY_NEEDED && YYCOPY_NEEDED
/* Copy COUNT objects from SRC to DST.  The source and destination do
   not overlap.  */
# ifndef YYCOPY
#  if defined __GNUC__ && 1 < __GNUC__
#   define YYCOPY(Dst, Src, Count) \
      __builtin_memcpy (Dst, Src, (Count) * sizeof (*(Src)))
#  else
#   define YYCOPY(Dst, Src, Count)              \
      do                                        \
        {                                       \
          YYSIZE_T yyi;                         \
          for (yyi = 0; yyi < (Count); yyi++)   \
            (Dst)[yyi] = (Src)[yyi];            \
        }                                       \
      while (0)
#  endif
# endif
#endif /* !YYCOPY_NEEDED */

/* YYFINAL -- State number of the termination state.  */
#define YYFINAL  7
/* YYLAST -- Last index in YYTABLE.  */
#define YYLAST   61

/* YYNTOKENS -- Number of terminals.  */
#define YYNTOKENS  29
/* YYNNTS -- Number of nonterminals.  */
#define YYNNTS  8
/* YYNRULES -- Number of rules.  */
#define YYNRULES  36
/* YYNSTATES -- Number of states.  */
#define YYNSTATES  45

/* YYTRANSLATE[YYX] -- Symbol number corresponding to YYX as returned
   by yylex, with out-of-bounds checking.  */
#define YYUNDEFTOK  2
#define YYMAXUTOK   283

#define YYTRANSLATE(YYX)                                                \
  ((unsigned int) (YYX) <= YYMAXUTOK ? yytranslate[YYX] : YYUNDEFTOK)

/* YYTRANSLATE[TOKEN-NUM] -- Symbol number corresponding to TOKEN-NUM
   as returned by yylex, without out-of-bounds checking.  */
static const yytype_uint8 yytranslate[] =
{
       0,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     2,     2,     2,     2,
       2,     2,     2,     2,     2,     2,     1,     2,     3,     4,
       5,     6,     7,     8,     9,    10,    11,    12,    13,    14,
      15,    16,    17,    18,    19,    20,    21,    22,    23,    24,
      25,    26,    27,    28
};

#if YYDEBUG
  /* YYRLINE[YYN] -- Source line where rule number YYN was defined.  */
static const yytype_uint16 yyrline[] =
{
       0,   147,   147,   150,   160,   164,   173,   177,   184,   188,
     197,   201,   205,   217,   220,   224,   231,   235,   239,   243,
     247,   251,   255,   259,   263,   267,   271,   275,   279,   283,
     287,   291,   295,   299,   303,   307,   311
};
#endif

#if YYDEBUG || YYERROR_VERBOSE || 0
/* YYTNAME[SYMBOL-NUM] -- String name of the symbol SYMBOL-NUM.
   First, the terminals, then, starting at YYNTOKENS, nonterminals.  */
static const char *const yytname[] =
{
  "$end", "error", "$undefined", "_ERROR_", "LCURLY_OP", "RCURLY_OP",
  "EQUAL_OP", "COMMA_OP", "SEMI_OP", "IDENTIFIER", "STRING", "DQUOTE",
  "SQUOTE", "TOKEN", "REGEX_TOKEN", "TOK_PATH", "TOK_TRUE", "TOK_FALSE",
  "TOK_DECNUM", "TOK_HEXNUM", "TOK_OCTNUM", "TOK_ARITH_OP", "TOK_V4_ANY",
  "TOK_V4ADDR", "TOK_V4CIDR", "TOK_V6ADDR", "TOK_V6CIDR", "TOK_FSID",
  "TOK_NETGROUP", "$accept", "config", "deflist", "definition",
  "statement", "block", "exprlist", "expression", YY_NULLPTR
};
#endif

# ifdef YYPRINT
/* YYTOKNUM[NUM] -- (External) token number corresponding to the
   (internal) symbol number NUM (which must be that of a token).  */
static const yytype_uint16 yytoknum[] =
{
       0,   256,   257,   258,   259,   260,   261,   262,   263,   264,
     265,   266,   267,   268,   269,   270,   271,   272,   273,   274,
     275,   276,   277,   278,   279,   280,   281,   282,   283
};
# endif

#define YYPACT_NINF -17

#define yypact_value_is_default(Yystate) \
  (!!((Yystate) == (-17)))

#define YYTABLE_NINF -14

#define yytable_value_is_error(Yytable_value) \
  0

  /* YYPACT[STATE-NUM] -- Index in YYTABLE of the portion describing
     STATE-NUM.  */
static const yytype_int8 yypact[] =
{
      44,    48,     8,    44,   -17,    46,    -1,   -17,   -17,    52,
     -17,    -4,   -17,    50,   -17,   -17,   -17,   -17,   -17,   -17,
     -17,   -17,   -17,   -17,   -17,   -16,   -17,   -17,   -17,   -17,
     -17,   -17,   -17,   -17,    42,   -17,   -17,   -17,   -17,   -17,
     -17,   -17,    18,   -17,   -17
};

  /* YYDEFACT[STATE-NUM] -- Default reduction number in state STATE-NUM.
     Performed when YYTABLE does not specify something else to do.  Zero
     means the default is an error.  */
static const yytype_uint8 yydefact[] =
{
       2,     0,     0,     3,     4,     0,     0,     1,     5,     0,
      10,     0,     7,     0,    19,    20,    21,    17,    18,    16,
      22,    23,    26,    25,    24,     0,    30,    31,    32,    33,
      34,    35,    36,     6,     0,    14,    12,    11,     9,    29,
      28,    27,     0,     8,    15
};

  /* YYPGOTO[NTERM-NUM].  */
static const yytype_int8 yypgoto[] =
{
     -17,   -17,    54,    45,   -17,   -17,   -17,    19
};

  /* YYDEFGOTO[NTERM-NUM].  */
static const yytype_int8 yydefgoto[] =
{
      -1,     2,     3,     4,    33,    12,    34,    35
};

  /* YYTABLE[YYPACT[STATE-NUM]] -- What to do in state STATE-NUM.  If
     positive, shift that token.  If negative, reduce the rule whose
     number is the opposite.  If YYTABLE_NINF, syntax error.  */
static const yytype_int8 yytable[] =
{
      13,    37,    39,    40,    41,     1,   -13,   -13,     7,    14,
      15,    16,    17,    18,    19,    20,    21,    22,    23,    24,
      25,    26,    27,    28,    29,    30,    31,    32,    14,    15,
      16,    17,    18,    19,    20,    21,    22,    23,    24,    25,
      26,    27,    28,    29,    30,    31,    32,     9,     8,    42,
      43,    10,     5,     1,     6,     1,     8,    36,    38,    11,
       0,    44
};

static const yytype_int8 yycheck[] =
{
       1,     5,    18,    19,    20,     9,     7,     8,     0,    10,
      11,    12,    13,    14,    15,    16,    17,    18,    19,    20,
      21,    22,    23,    24,    25,    26,    27,    28,    10,    11,
      12,    13,    14,    15,    16,    17,    18,    19,    20,    21,
      22,    23,    24,    25,    26,    27,    28,     1,     3,     7,
       8,     5,     4,     9,     6,     9,    11,     5,     8,     5,
      -1,    42
};

  /* YYSTOS[STATE-NUM] -- The (internal number of the) accessing
     symbol of state STATE-NUM.  */
static const yytype_uint8 yystos[] =
{
       0,     9,    30,    31,    32,     4,     6,     0,    32,     1,
       5,    31,    34,     1,    10,    11,    12,    13,    14,    15,
      16,    17,    18,    19,    20,    21,    22,    23,    24,    25,
      26,    27,    28,    33,    35,    36,     5,     5,     8,    18,
      19,    20,     7,     8,    36
};

  /* YYR1[YYN] -- Symbol number of symbol that rule YYN derives.  */
static const yytype_uint8 yyr1[] =
{
       0,    29,    30,    30,    31,    31,    32,    32,    33,    33,
      34,    34,    34,    35,    35,    35,    36,    36,    36,    36,
      36,    36,    36,    36,    36,    36,    36,    36,    36,    36,
      36,    36,    36,    36,    36,    36,    36
};

  /* YYR2[YYN] -- Number of symbols on the right hand side of rule YYN.  */
static const yytype_uint8 yyr2[] =
{
       0,     2,     0,     1,     1,     2,     3,     3,     2,     2,
       1,     2,     2,     0,     1,     3,     1,     1,     1,     1,
       1,     1,     1,     1,     1,     1,     1,     2,     2,     2,
       1,     1,     1,     1,     1,     1,     1
};


#define yyerrok         (yyerrstatus = 0)
#define yyclearin       (yychar = YYEMPTY)
#define YYEMPTY         (-2)
#define YYEOF           0

#define YYACCEPT        goto yyacceptlab
#define YYABORT         goto yyabortlab
#define YYERROR         goto yyerrorlab


#define YYRECOVERING()  (!!yyerrstatus)

#define YYBACKUP(Token, Value)                                  \
do                                                              \
  if (yychar == YYEMPTY)                                        \
    {                                                           \
      yychar = (Token);                                         \
      yylval = (Value);                                         \
      YYPOPSTACK (yylen);                                       \
      yystate = *yyssp;                                         \
      goto yybackup;                                            \
    }                                                           \
  else                                                          \
    {                                                           \
      yyerror (&yylloc, st, YY_("syntax error: cannot back up")); \
      YYERROR;                                                  \
    }                                                           \
while (0)

/* Error token number */
#define YYTERROR        1
#define YYERRCODE       256


/* YYLLOC_DEFAULT -- Set CURRENT to span from RHS[1] to RHS[N].
   If N is 0, then set CURRENT to the empty location which ends
   the previous symbol: RHS[0] (always defined).  */

#ifndef YYLLOC_DEFAULT
# define YYLLOC_DEFAULT(Current, Rhs, N)                                \
    do                                                                  \
      if (N)                                                            \
        {                                                               \
          (Current).first_line   = YYRHSLOC (Rhs, 1).first_line;        \
          (Current).first_column = YYRHSLOC (Rhs, 1).first_column;      \
          (Current).last_line    = YYRHSLOC (Rhs, N).last_line;         \
          (Current).last_column  = YYRHSLOC (Rhs, N).last_column;       \
        }                                                               \
      else                                                              \
        {                                                               \
          (Current).first_line   = (Current).last_line   =              \
            YYRHSLOC (Rhs, 0).last_line;                                \
          (Current).first_column = (Current).last_column =              \
            YYRHSLOC (Rhs, 0).last_column;                              \
        }                                                               \
    while (0)
#endif

#define YYRHSLOC(Rhs, K) ((Rhs)[K])


/* Enable debugging if requested.  */
#if YYDEBUG

# ifndef YYFPRINTF
#  include <stdio.h> /* INFRINGES ON USER NAME SPACE */
#  define YYFPRINTF fprintf
# endif

# define YYDPRINTF(Args)                        \
do {                                            \
  if (yydebug)                                  \
    YYFPRINTF Args;                             \
} while (0)


/* YY_LOCATION_PRINT -- Print the location on the stream.
   This macro was not mandated originally: define only if we know
   we won't break user code: when these are the locations we know.  */

#ifndef YY_LOCATION_PRINT
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL

/* Print *YYLOCP on YYO.  Private, do not rely on its existence. */

YY_ATTRIBUTE_UNUSED
static unsigned
yy_location_print_ (FILE *yyo, YYLTYPE const * const yylocp)
{
  unsigned res = 0;
  int end_col = 0 != yylocp->last_column ? yylocp->last_column - 1 : 0;
  if (0 <= yylocp->first_line)
    {
      res += YYFPRINTF (yyo, "%d", yylocp->first_line);
      if (0 <= yylocp->first_column)
        res += YYFPRINTF (yyo, ".%d", yylocp->first_column);
    }
  if (0 <= yylocp->last_line)
    {
      if (yylocp->first_line < yylocp->last_line)
        {
          res += YYFPRINTF (yyo, "-%d", yylocp->last_line);
          if (0 <= end_col)
            res += YYFPRINTF (yyo, ".%d", end_col);
        }
      else if (0 <= end_col && yylocp->first_column < end_col)
        res += YYFPRINTF (yyo, "-%d", end_col);
    }
  return res;
 }

#  define YY_LOCATION_PRINT(File, Loc)          \
  yy_location_print_ (File, &(Loc))

# else
#  define YY_LOCATION_PRINT(File, Loc) ((void) 0)
# endif
#endif


# define YY_SYMBOL_PRINT(Title, Type, Value, Location)                    \
do {                                                                      \
  if (yydebug)                                                            \
    {                                                                     \
      YYFPRINTF (stderr, "%s ", Title);                                   \
      yy_symbol_print (stderr,                                            \
                  Type, Value, Location, st); \
      YYFPRINTF (stderr, "\n");                                           \
    }                                                                     \
} while (0)


/*----------------------------------------.
| Print this symbol's value on YYOUTPUT.  |
`----------------------------------------*/

static void
yy_symbol_value_print (FILE *yyoutput, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp, struct parser_state *st)
{
  FILE *yyo = yyoutput;
  YYUSE (yyo);
  YYUSE (yylocationp);
  YYUSE (st);
  if (!yyvaluep)
    return;
# ifdef YYPRINT
  if (yytype < YYNTOKENS)
    YYPRINT (yyoutput, yytoknum[yytype], *yyvaluep);
# endif
  YYUSE (yytype);
}


/*--------------------------------.
| Print this symbol on YYOUTPUT.  |
`--------------------------------*/

static void
yy_symbol_print (FILE *yyoutput, int yytype, YYSTYPE const * const yyvaluep, YYLTYPE const * const yylocationp, struct parser_state *st)
{
  YYFPRINTF (yyoutput, "%s %s (",
             yytype < YYNTOKENS ? "token" : "nterm", yytname[yytype]);

  YY_LOCATION_PRINT (yyoutput, *yylocationp);
  YYFPRINTF (yyoutput, ": ");
  yy_symbol_value_print (yyoutput, yytype, yyvaluep, yylocationp, st);
  YYFPRINTF (yyoutput, ")");
}

/*------------------------------------------------------------------.
| yy_stack_print -- Print the state stack from its BOTTOM up to its |
| TOP (included).                                                   |
`------------------------------------------------------------------*/

static void
yy_stack_print (yytype_int16 *yybottom, yytype_int16 *yytop)
{
  YYFPRINTF (stderr, "Stack now");
  for (; yybottom <= yytop; yybottom++)
    {
      int yybot = *yybottom;
      YYFPRINTF (stderr, " %d", yybot);
    }
  YYFPRINTF (stderr, "\n");
}

# define YY_STACK_PRINT(Bottom, Top)                            \
do {                                                            \
  if (yydebug)                                                  \
    yy_stack_print ((Bottom), (Top));                           \
} while (0)


/*------------------------------------------------.
| Report that the YYRULE is going to be reduced.  |
`------------------------------------------------*/

static void
yy_reduce_print (yytype_int16 *yyssp, YYSTYPE *yyvsp, YYLTYPE *yylsp, int yyrule, struct parser_state *st)
{
  unsigned long int yylno = yyrline[yyrule];
  int yynrhs = yyr2[yyrule];
  int yyi;
  YYFPRINTF (stderr, "Reducing stack by rule %d (line %lu):\n",
             yyrule - 1, yylno);
  /* The symbols being reduced.  */
  for (yyi = 0; yyi < yynrhs; yyi++)
    {
      YYFPRINTF (stderr, "   $%d = ", yyi + 1);
      yy_symbol_print (stderr,
                       yystos[yyssp[yyi + 1 - yynrhs]],
                       &(yyvsp[(yyi + 1) - (yynrhs)])
                       , &(yylsp[(yyi + 1) - (yynrhs)])                       , st);
      YYFPRINTF (stderr, "\n");
    }
}

# define YY_REDUCE_PRINT(Rule)          \
do {                                    \
  if (yydebug)                          \
    yy_reduce_print (yyssp, yyvsp, yylsp, Rule, st); \
} while (0)

/* Nonzero means print parse trace.  It is left uninitialized so that
   multiple parsers can coexist.  */
int yydebug;
#else /* !YYDEBUG */
# define YYDPRINTF(Args)
# define YY_SYMBOL_PRINT(Title, Type, Value, Location)
# define YY_STACK_PRINT(Bottom, Top)
# define YY_REDUCE_PRINT(Rule)
#endif /* !YYDEBUG */


/* YYINITDEPTH -- initial size of the parser's stacks.  */
#ifndef YYINITDEPTH
# define YYINITDEPTH 200
#endif

/* YYMAXDEPTH -- maximum size the stacks can grow to (effective only
   if the built-in stack extension method is used).

   Do not make this value too large; the results are undefined if
   YYSTACK_ALLOC_MAXIMUM < YYSTACK_BYTES (YYMAXDEPTH)
   evaluated with infinite-precision integer arithmetic.  */

#ifndef YYMAXDEPTH
# define YYMAXDEPTH 10000
#endif


#if YYERROR_VERBOSE

# ifndef yystrlen
#  if defined __GLIBC__ && defined _STRING_H
#   define yystrlen strlen
#  else
/* Return the length of YYSTR.  */
static YYSIZE_T
yystrlen (const char *yystr)
{
  YYSIZE_T yylen;
  for (yylen = 0; yystr[yylen]; yylen++)
    continue;
  return yylen;
}
#  endif
# endif

# ifndef yystpcpy
#  if defined __GLIBC__ && defined _STRING_H && defined _GNU_SOURCE
#   define yystpcpy stpcpy
#  else
/* Copy YYSRC to YYDEST, returning the address of the terminating '\0' in
   YYDEST.  */
static char *
yystpcpy (char *yydest, const char *yysrc)
{
  char *yyd = yydest;
  const char *yys = yysrc;

  while ((*yyd++ = *yys++) != '\0')
    continue;

  return yyd - 1;
}
#  endif
# endif

# ifndef yytnamerr
/* Copy to YYRES the contents of YYSTR after stripping away unnecessary
   quotes and backslashes, so that it's suitable for yyerror.  The
   heuristic is that double-quoting is unnecessary unless the string
   contains an apostrophe, a comma, or backslash (other than
   backslash-backslash).  YYSTR is taken from yytname.  If YYRES is
   null, do not copy; instead, return the length of what the result
   would have been.  */
static YYSIZE_T
yytnamerr (char *yyres, const char *yystr)
{
  if (*yystr == '"')
    {
      YYSIZE_T yyn = 0;
      char const *yyp = yystr;

      for (;;)
        switch (*++yyp)
          {
          case '\'':
          case ',':
            goto do_not_strip_quotes;

          case '\\':
            if (*++yyp != '\\')
              goto do_not_strip_quotes;
            /* Fall through.  */
          default:
            if (yyres)
              yyres[yyn] = *yyp;
            yyn++;
            break;

          case '"':
            if (yyres)
              yyres[yyn] = '\0';
            return yyn;
          }
    do_not_strip_quotes: ;
    }

  if (! yyres)
    return yystrlen (yystr);

  return yystpcpy (yyres, yystr) - yyres;
}
# endif

/* Copy into *YYMSG, which is of size *YYMSG_ALLOC, an error message
   about the unexpected token YYTOKEN for the state stack whose top is
   YYSSP.

   Return 0 if *YYMSG was successfully written.  Return 1 if *YYMSG is
   not large enough to hold the message.  In that case, also set
   *YYMSG_ALLOC to the required number of bytes.  Return 2 if the
   required number of bytes is too large to store.  */
static int
yysyntax_error (YYSIZE_T *yymsg_alloc, char **yymsg,
                yytype_int16 *yyssp, int yytoken)
{
  YYSIZE_T yysize0 = yytnamerr (YY_NULLPTR, yytname[yytoken]);
  YYSIZE_T yysize = yysize0;
  enum { YYERROR_VERBOSE_ARGS_MAXIMUM = 5 };
  /* Internationalized format string. */
  const char *yyformat = YY_NULLPTR;
  /* Arguments of yyformat. */
  char const *yyarg[YYERROR_VERBOSE_ARGS_MAXIMUM];
  /* Number of reported tokens (one for the "unexpected", one per
     "expected"). */
  int yycount = 0;

  /* There are many possibilities here to consider:
     - If this state is a consistent state with a default action, then
       the only way this function was invoked is if the default action
       is an error action.  In that case, don't check for expected
       tokens because there are none.
     - The only way there can be no lookahead present (in yychar) is if
       this state is a consistent state with a default action.  Thus,
       detecting the absence of a lookahead is sufficient to determine
       that there is no unexpected or expected token to report.  In that
       case, just report a simple "syntax error".
     - Don't assume there isn't a lookahead just because this state is a
       consistent state with a default action.  There might have been a
       previous inconsistent state, consistent state with a non-default
       action, or user semantic action that manipulated yychar.
     - Of course, the expected token list depends on states to have
       correct lookahead information, and it depends on the parser not
       to perform extra reductions after fetching a lookahead from the
       scanner and before detecting a syntax error.  Thus, state merging
       (from LALR or IELR) and default reductions corrupt the expected
       token list.  However, the list is correct for canonical LR with
       one exception: it will still contain any token that will not be
       accepted due to an error action in a later state.
  */
  if (yytoken != YYEMPTY)
    {
      int yyn = yypact[*yyssp];
      yyarg[yycount++] = yytname[yytoken];
      if (!yypact_value_is_default (yyn))
        {
          /* Start YYX at -YYN if negative to avoid negative indexes in
             YYCHECK.  In other words, skip the first -YYN actions for
             this state because they are default actions.  */
          int yyxbegin = yyn < 0 ? -yyn : 0;
          /* Stay within bounds of both yycheck and yytname.  */
          int yychecklim = YYLAST - yyn + 1;
          int yyxend = yychecklim < YYNTOKENS ? yychecklim : YYNTOKENS;
          int yyx;

          for (yyx = yyxbegin; yyx < yyxend; ++yyx)
            if (yycheck[yyx + yyn] == yyx && yyx != YYTERROR
                && !yytable_value_is_error (yytable[yyx + yyn]))
              {
                if (yycount == YYERROR_VERBOSE_ARGS_MAXIMUM)
                  {
                    yycount = 1;
                    yysize = yysize0;
                    break;
                  }
                yyarg[yycount++] = yytname[yyx];
                {
                  YYSIZE_T yysize1 = yysize + yytnamerr (YY_NULLPTR, yytname[yyx]);
                  if (! (yysize <= yysize1
                         && yysize1 <= YYSTACK_ALLOC_MAXIMUM))
                    return 2;
                  yysize = yysize1;
                }
              }
        }
    }

  switch (yycount)
    {
# define YYCASE_(N, S)                      \
      case N:                               \
        yyformat = S;                       \
      break
      YYCASE_(0, YY_("syntax error"));
      YYCASE_(1, YY_("syntax error, unexpected %s"));
      YYCASE_(2, YY_("syntax error, unexpected %s, expecting %s"));
      YYCASE_(3, YY_("syntax error, unexpected %s, expecting %s or %s"));
      YYCASE_(4, YY_("syntax error, unexpected %s, expecting %s or %s or %s"));
      YYCASE_(5, YY_("syntax error, unexpected %s, expecting %s or %s or %s or %s"));
# undef YYCASE_
    }

  {
    YYSIZE_T yysize1 = yysize + yystrlen (yyformat);
    if (! (yysize <= yysize1 && yysize1 <= YYSTACK_ALLOC_MAXIMUM))
      return 2;
    yysize = yysize1;
  }

  if (*yymsg_alloc < yysize)
    {
      *yymsg_alloc = 2 * yysize;
      if (! (yysize <= *yymsg_alloc
             && *yymsg_alloc <= YYSTACK_ALLOC_MAXIMUM))
        *yymsg_alloc = YYSTACK_ALLOC_MAXIMUM;
      return 1;
    }

  /* Avoid sprintf, as that infringes on the user's name space.
     Don't have undefined behavior even if the translation
     produced a string with the wrong number of "%s"s.  */
  {
    char *yyp = *yymsg;
    int yyi = 0;
    while ((*yyp = *yyformat) != '\0')
      if (*yyp == '%' && yyformat[1] == 's' && yyi < yycount)
        {
          yyp += yytnamerr (yyp, yyarg[yyi++]);
          yyformat += 2;
        }
      else
        {
          yyp++;
          yyformat++;
        }
  }
  return 0;
}
#endif /* YYERROR_VERBOSE */

/*-----------------------------------------------.
| Release the memory associated to this symbol.  |
`-----------------------------------------------*/

static void
yydestruct (const char *yymsg, int yytype, YYSTYPE *yyvaluep, YYLTYPE *yylocationp, struct parser_state *st)
{
  YYUSE (yyvaluep);
  YYUSE (yylocationp);
  YYUSE (st);
  if (!yymsg)
    yymsg = "Deleting";
  YY_SYMBOL_PRINT (yymsg, yytype, yyvaluep, yylocationp);

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  YYUSE (yytype);
  YY_IGNORE_MAYBE_UNINITIALIZED_END
}




/*----------.
| yyparse.  |
`----------*/

int
yyparse (struct parser_state *st)
{
/* The lookahead symbol.  */
int yychar;


/* The semantic value of the lookahead symbol.  */
/* Default value used for initialization, for pacifying older GCCs
   or non-GCC compilers.  */
YY_INITIAL_VALUE (static YYSTYPE yyval_default;)
YYSTYPE yylval YY_INITIAL_VALUE (= yyval_default);

/* Location data for the lookahead symbol.  */
static YYLTYPE yyloc_default
# if defined YYLTYPE_IS_TRIVIAL && YYLTYPE_IS_TRIVIAL
  = { 1, 1, 1, 1 }
# endif
;
YYLTYPE yylloc = yyloc_default;

    /* Number of syntax errors so far.  */
    int yynerrs;

    int yystate;
    /* Number of tokens to shift before error messages enabled.  */
    int yyerrstatus;

    /* The stacks and their tools:
       'yyss': related to states.
       'yyvs': related to semantic values.
       'yyls': related to locations.

       Refer to the stacks through separate pointers, to allow yyoverflow
       to reallocate them elsewhere.  */

    /* The state stack.  */
    yytype_int16 yyssa[YYINITDEPTH];
    yytype_int16 *yyss;
    yytype_int16 *yyssp;

    /* The semantic value stack.  */
    YYSTYPE yyvsa[YYINITDEPTH];
    YYSTYPE *yyvs;
    YYSTYPE *yyvsp;

    /* The location stack.  */
    YYLTYPE yylsa[YYINITDEPTH];
    YYLTYPE *yyls;
    YYLTYPE *yylsp;

    /* The locations where the error started and ended.  */
    YYLTYPE yyerror_range[3];

    YYSIZE_T yystacksize;

  int yyn;
  int yyresult;
  /* Lookahead token as an internal (translated) token number.  */
  int yytoken = 0;
  /* The variables used to return semantic value and location from the
     action routines.  */
  YYSTYPE yyval;
  YYLTYPE yyloc;

#if YYERROR_VERBOSE
  /* Buffer for error messages, and its allocated size.  */
  char yymsgbuf[128];
  char *yymsg = yymsgbuf;
  YYSIZE_T yymsg_alloc = sizeof yymsgbuf;
#endif

#define YYPOPSTACK(N)   (yyvsp -= (N), yyssp -= (N), yylsp -= (N))

  /* The number of symbols on the RHS of the reduced rule.
     Keep to zero when no symbol should be popped.  */
  int yylen = 0;

  yyssp = yyss = yyssa;
  yyvsp = yyvs = yyvsa;
  yylsp = yyls = yylsa;
  yystacksize = YYINITDEPTH;

  YYDPRINTF ((stderr, "Starting parse\n"));

  yystate = 0;
  yyerrstatus = 0;
  yynerrs = 0;
  yychar = YYEMPTY; /* Cause a token to be read.  */
  yylsp[0] = yylloc;
  goto yysetstate;

/*------------------------------------------------------------.
| yynewstate -- Push a new state, which is found in yystate.  |
`------------------------------------------------------------*/
 yynewstate:
  /* In all cases, when you get here, the value and location stacks
     have just been pushed.  So pushing a state here evens the stacks.  */
  yyssp++;

 yysetstate:
  *yyssp = yystate;

  if (yyss + yystacksize - 1 <= yyssp)
    {
      /* Get the current used size of the three stacks, in elements.  */
      YYSIZE_T yysize = yyssp - yyss + 1;

#ifdef yyoverflow
      {
        /* Give user a chance to reallocate the stack.  Use copies of
           these so that the &'s don't force the real ones into
           memory.  */
        YYSTYPE *yyvs1 = yyvs;
        yytype_int16 *yyss1 = yyss;
        YYLTYPE *yyls1 = yyls;

        /* Each stack pointer address is followed by the size of the
           data in use in that stack, in bytes.  This used to be a
           conditional around just the two extra args, but that might
           be undefined if yyoverflow is a macro.  */
        yyoverflow (YY_("memory exhausted"),
                    &yyss1, yysize * sizeof (*yyssp),
                    &yyvs1, yysize * sizeof (*yyvsp),
                    &yyls1, yysize * sizeof (*yylsp),
                    &yystacksize);

        yyls = yyls1;
        yyss = yyss1;
        yyvs = yyvs1;
      }
#else /* no yyoverflow */
# ifndef YYSTACK_RELOCATE
      goto yyexhaustedlab;
# else
      /* Extend the stack our own way.  */
      if (YYMAXDEPTH <= yystacksize)
        goto yyexhaustedlab;
      yystacksize *= 2;
      if (YYMAXDEPTH < yystacksize)
        yystacksize = YYMAXDEPTH;

      {
        yytype_int16 *yyss1 = yyss;
        union yyalloc *yyptr =
          (union yyalloc *) YYSTACK_ALLOC (YYSTACK_BYTES (yystacksize));
        if (! yyptr)
          goto yyexhaustedlab;
        YYSTACK_RELOCATE (yyss_alloc, yyss);
        YYSTACK_RELOCATE (yyvs_alloc, yyvs);
        YYSTACK_RELOCATE (yyls_alloc, yyls);
#  undef YYSTACK_RELOCATE
        if (yyss1 != yyssa)
          YYSTACK_FREE (yyss1);
      }
# endif
#endif /* no yyoverflow */

      yyssp = yyss + yysize - 1;
      yyvsp = yyvs + yysize - 1;
      yylsp = yyls + yysize - 1;

      YYDPRINTF ((stderr, "Stack size increased to %lu\n",
                  (unsigned long int) yystacksize));

      if (yyss + yystacksize - 1 <= yyssp)
        YYABORT;
    }

  YYDPRINTF ((stderr, "Entering state %d\n", yystate));

  if (yystate == YYFINAL)
    YYACCEPT;

  goto yybackup;

/*-----------.
| yybackup.  |
`-----------*/
yybackup:

  /* Do appropriate processing given the current state.  Read a
     lookahead token if we need one and don't already have one.  */

  /* First try to decide what to do without reference to lookahead token.  */
  yyn = yypact[yystate];
  if (yypact_value_is_default (yyn))
    goto yydefault;

  /* Not known => get a lookahead token if don't already have one.  */

  /* YYCHAR is either YYEMPTY or YYEOF or a valid lookahead symbol.  */
  if (yychar == YYEMPTY)
    {
      YYDPRINTF ((stderr, "Reading a token: "));
      yychar = yylex (&yylval, &yylloc, st);
    }

  if (yychar <= YYEOF)
    {
      yychar = yytoken = YYEOF;
      YYDPRINTF ((stderr, "Now at end of input.\n"));
    }
  else
    {
      yytoken = YYTRANSLATE (yychar);
      YY_SYMBOL_PRINT ("Next token is", yytoken, &yylval, &yylloc);
    }

  /* If the proper action on seeing token YYTOKEN is to reduce or to
     detect an error, take that action.  */
  yyn += yytoken;
  if (yyn < 0 || YYLAST < yyn || yycheck[yyn] != yytoken)
    goto yydefault;
  yyn = yytable[yyn];
  if (yyn <= 0)
    {
      if (yytable_value_is_error (yyn))
        goto yyerrlab;
      yyn = -yyn;
      goto yyreduce;
    }

  /* Count tokens shifted since error; after three, turn off error
     status.  */
  if (yyerrstatus)
    yyerrstatus--;

  /* Shift the lookahead token.  */
  YY_SYMBOL_PRINT ("Shifting", yytoken, &yylval, &yylloc);

  /* Discard the shifted token.  */
  yychar = YYEMPTY;

  yystate = yyn;
  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END
  *++yylsp = yylloc;
  goto yynewstate;


/*-----------------------------------------------------------.
| yydefault -- do the default action for the current state.  |
`-----------------------------------------------------------*/
yydefault:
  yyn = yydefact[yystate];
  if (yyn == 0)
    goto yyerrlab;
  goto yyreduce;


/*-----------------------------.
| yyreduce -- Do a reduction.  |
`-----------------------------*/
yyreduce:
  /* yyn is the number of a rule to reduce with.  */
  yylen = yyr2[yyn];

  /* If YYLEN is nonzero, implement the default value of the action:
     '$$ = $1'.

     Otherwise, the following line sets YYVAL to garbage.
     This behavior is undocumented and Bison
     users should not rely upon it.  Assigning to YYVAL
     unconditionally makes the parser a bit smaller, and it avoids a
     GCC warning that YYVAL may be used uninitialized.  */
  yyval = yyvsp[1-yylen];

  /* Default location.  */
  YYLLOC_DEFAULT (yyloc, (yylsp - yylen), yylen);
  YY_REDUCE_PRINT (yyn);
  switch (yyn)
    {
        case 2:
#line 147 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    { /* empty */
  config_parse_error(&yyloc, st, "Empty configuration file");
}
#line 1492 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 3:
#line 151 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  if ((yyvsp[0].node) != NULL)
    glist_add_tail(&((yyvsp[0].node))->node, &st->root_node->root.u.nterm.sub_nodes);
  link_node(&st->root_node->root);
}
#line 1502 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 4:
#line 161 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = (yyvsp[0].node);
}
#line 1510 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 5:
#line 165 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = link_sibling((yyvsp[-1].node), (yyvsp[0].node));
}
#line 1518 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 6:
#line 174 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_stmt((yyvsp[-2].token), (yyvsp[0].node), &(yyloc), st);
}
#line 1526 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 7:
#line 178 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node)=config_block((yyvsp[-2].token), (yyvsp[0].node), &(yyloc), st);
}
#line 1534 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 8:
#line 185 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = (yyvsp[-1].node);
}
#line 1542 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 9:
#line 189 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  config_parse_error(&(yyloc), st, "Syntax error in statement");
  yyerrok;
  (yyval.node) = NULL;
}
#line 1552 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 10:
#line 198 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    { /* empty */
  (yyval.node) = NULL;
}
#line 1560 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 11:
#line 202 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = (yyvsp[-1].node);
}
#line 1568 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 12:
#line 206 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  config_parse_error(&(yyloc), st, "Syntax error in block");
  yyerrok;
  (yyval.node) = NULL;
}
#line 1578 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 13:
#line 217 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    { /* empty */
  (yyval.node) = NULL;
}
#line 1586 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 14:
#line 221 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = (yyvsp[0].node);
}
#line 1594 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 15:
#line 225 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = link_sibling((yyvsp[-2].node), (yyvsp[0].node));
}
#line 1602 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 16:
#line 232 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_PATH, &(yyloc), st);
}
#line 1610 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 17:
#line 236 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_TOKEN, &(yyloc), st);
}
#line 1618 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 18:
#line 240 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_REGEX, &(yyloc), st);
}
#line 1626 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 19:
#line 244 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_STRING, &(yyloc), st);
}
#line 1634 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 20:
#line 248 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_DQUOTE, &(yyloc), st);
}
#line 1642 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 21:
#line 252 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_SQUOTE, &(yyloc), st);
}
#line 1650 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 22:
#line 256 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_TRUE, &(yyloc), st);
}
#line 1658 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 23:
#line 260 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_FALSE, &(yyloc), st);
}
#line 1666 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 24:
#line 264 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_OCTNUM, &(yyloc), st);
}
#line 1674 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 25:
#line 268 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_HEXNUM, &(yyloc), st);
}
#line 1682 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 26:
#line 272 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_DECNUM, &(yyloc), st);
}
#line 1690 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 27:
#line 276 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term((yyvsp[-1].token), (yyvsp[0].token), TERM_OCTNUM, &(yyloc), st);
}
#line 1698 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 28:
#line 280 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term((yyvsp[-1].token), (yyvsp[0].token), TERM_HEXNUM, &(yyloc), st);
}
#line 1706 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 29:
#line 284 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term((yyvsp[-1].token), (yyvsp[0].token), TERM_DECNUM, &(yyloc), st);
}
#line 1714 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 30:
#line 288 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_V4_ANY, &(yyloc), st);
}
#line 1722 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 31:
#line 292 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_V4ADDR, &(yyloc), st);
}
#line 1730 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 32:
#line 296 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_V4CIDR, &(yyloc), st);
}
#line 1738 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 33:
#line 300 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_V6ADDR, &(yyloc), st);
}
#line 1746 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 34:
#line 304 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_V6CIDR, &(yyloc), st);
}
#line 1754 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 35:
#line 308 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_FSID, &(yyloc), st);
}
#line 1762 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;

  case 36:
#line 312 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1646  */
    {
  (yyval.node) = config_term(NULL, (yyvsp[0].token), TERM_NETGROUP, &(yyloc), st);
}
#line 1770 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
    break;


#line 1774 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.c" /* yacc.c:1646  */
      default: break;
    }
  /* User semantic actions sometimes alter yychar, and that requires
     that yytoken be updated with the new translation.  We take the
     approach of translating immediately before every use of yytoken.
     One alternative is translating here after every semantic action,
     but that translation would be missed if the semantic action invokes
     YYABORT, YYACCEPT, or YYERROR immediately after altering yychar or
     if it invokes YYBACKUP.  In the case of YYABORT or YYACCEPT, an
     incorrect destructor might then be invoked immediately.  In the
     case of YYERROR or YYBACKUP, subsequent parser actions might lead
     to an incorrect destructor call or verbose syntax error message
     before the lookahead is translated.  */
  YY_SYMBOL_PRINT ("-> $$ =", yyr1[yyn], &yyval, &yyloc);

  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);

  *++yyvsp = yyval;
  *++yylsp = yyloc;

  /* Now 'shift' the result of the reduction.  Determine what state
     that goes to, based on the state we popped back to and the rule
     number reduced by.  */

  yyn = yyr1[yyn];

  yystate = yypgoto[yyn - YYNTOKENS] + *yyssp;
  if (0 <= yystate && yystate <= YYLAST && yycheck[yystate] == *yyssp)
    yystate = yytable[yystate];
  else
    yystate = yydefgoto[yyn - YYNTOKENS];

  goto yynewstate;


/*--------------------------------------.
| yyerrlab -- here on detecting error.  |
`--------------------------------------*/
yyerrlab:
  /* Make sure we have latest lookahead translation.  See comments at
     user semantic actions for why this is necessary.  */
  yytoken = yychar == YYEMPTY ? YYEMPTY : YYTRANSLATE (yychar);

  /* If not already recovering from an error, report this error.  */
  if (!yyerrstatus)
    {
      ++yynerrs;
#if ! YYERROR_VERBOSE
      yyerror (&yylloc, st, YY_("syntax error"));
#else
# define YYSYNTAX_ERROR yysyntax_error (&yymsg_alloc, &yymsg, \
                                        yyssp, yytoken)
      {
        char const *yymsgp = YY_("syntax error");
        int yysyntax_error_status;
        yysyntax_error_status = YYSYNTAX_ERROR;
        if (yysyntax_error_status == 0)
          yymsgp = yymsg;
        else if (yysyntax_error_status == 1)
          {
            if (yymsg != yymsgbuf)
              YYSTACK_FREE (yymsg);
            yymsg = (char *) YYSTACK_ALLOC (yymsg_alloc);
            if (!yymsg)
              {
                yymsg = yymsgbuf;
                yymsg_alloc = sizeof yymsgbuf;
                yysyntax_error_status = 2;
              }
            else
              {
                yysyntax_error_status = YYSYNTAX_ERROR;
                yymsgp = yymsg;
              }
          }
        yyerror (&yylloc, st, yymsgp);
        if (yysyntax_error_status == 2)
          goto yyexhaustedlab;
      }
# undef YYSYNTAX_ERROR
#endif
    }

  yyerror_range[1] = yylloc;

  if (yyerrstatus == 3)
    {
      /* If just tried and failed to reuse lookahead token after an
         error, discard it.  */

      if (yychar <= YYEOF)
        {
          /* Return failure if at end of input.  */
          if (yychar == YYEOF)
            YYABORT;
        }
      else
        {
          yydestruct ("Error: discarding",
                      yytoken, &yylval, &yylloc, st);
          yychar = YYEMPTY;
        }
    }

  /* Else will try to reuse lookahead token after shifting the error
     token.  */
  goto yyerrlab1;


/*---------------------------------------------------.
| yyerrorlab -- error raised explicitly by YYERROR.  |
`---------------------------------------------------*/
yyerrorlab:

  /* Pacify compilers like GCC when the user code never invokes
     YYERROR and the label yyerrorlab therefore never appears in user
     code.  */
  if (/*CONSTCOND*/ 0)
     goto yyerrorlab;

  yyerror_range[1] = yylsp[1-yylen];
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYERROR.  */
  YYPOPSTACK (yylen);
  yylen = 0;
  YY_STACK_PRINT (yyss, yyssp);
  yystate = *yyssp;
  goto yyerrlab1;


/*-------------------------------------------------------------.
| yyerrlab1 -- common code for both syntax error and YYERROR.  |
`-------------------------------------------------------------*/
yyerrlab1:
  yyerrstatus = 3;      /* Each real token shifted decrements this.  */

  for (;;)
    {
      yyn = yypact[yystate];
      if (!yypact_value_is_default (yyn))
        {
          yyn += YYTERROR;
          if (0 <= yyn && yyn <= YYLAST && yycheck[yyn] == YYTERROR)
            {
              yyn = yytable[yyn];
              if (0 < yyn)
                break;
            }
        }

      /* Pop the current state because it cannot handle the error token.  */
      if (yyssp == yyss)
        YYABORT;

      yyerror_range[1] = *yylsp;
      yydestruct ("Error: popping",
                  yystos[yystate], yyvsp, yylsp, st);
      YYPOPSTACK (1);
      yystate = *yyssp;
      YY_STACK_PRINT (yyss, yyssp);
    }

  YY_IGNORE_MAYBE_UNINITIALIZED_BEGIN
  *++yyvsp = yylval;
  YY_IGNORE_MAYBE_UNINITIALIZED_END

  yyerror_range[2] = yylloc;
  /* Using YYLLOC is tempting, but would change the location of
     the lookahead.  YYLOC is available though.  */
  YYLLOC_DEFAULT (yyloc, yyerror_range, 2);
  *++yylsp = yyloc;

  /* Shift the error token.  */
  YY_SYMBOL_PRINT ("Shifting", yystos[yyn], yyvsp, yylsp);

  yystate = yyn;
  goto yynewstate;


/*-------------------------------------.
| yyacceptlab -- YYACCEPT comes here.  |
`-------------------------------------*/
yyacceptlab:
  yyresult = 0;
  goto yyreturn;

/*-----------------------------------.
| yyabortlab -- YYABORT comes here.  |
`-----------------------------------*/
yyabortlab:
  yyresult = 1;
  goto yyreturn;

#if !defined yyoverflow || YYERROR_VERBOSE
/*-------------------------------------------------.
| yyexhaustedlab -- memory exhaustion comes here.  |
`-------------------------------------------------*/
yyexhaustedlab:
  yyerror (&yylloc, st, YY_("memory exhausted"));
  yyresult = 2;
  /* Fall through.  */
#endif

yyreturn:
  if (yychar != YYEMPTY)
    {
      /* Make sure we have latest lookahead translation.  See comments at
         user semantic actions for why this is necessary.  */
      yytoken = YYTRANSLATE (yychar);
      yydestruct ("Cleanup: discarding lookahead",
                  yytoken, &yylval, &yylloc, st);
    }
  /* Do not reclaim the symbols of the rule whose action triggered
     this YYABORT or YYACCEPT.  */
  YYPOPSTACK (yylen);
  YY_STACK_PRINT (yyss, yyssp);
  while (yyssp != yyss)
    {
      yydestruct ("Cleanup: popping",
                  yystos[*yyssp], yyvsp, yylsp, st);
      YYPOPSTACK (1);
    }
#ifndef yyoverflow
  if (yyss != yyssa)
    YYSTACK_FREE (yyss);
#endif
#if YYERROR_VERBOSE
  if (yymsg != yymsgbuf)
    YYSTACK_FREE (yymsg);
#endif
  return yyresult;
}
#line 317 "/root/rpmbuild/BUILD/gnfs-4.0-7.1.10.20/config_parsing/conf_yacc.y" /* yacc.c:1906  */


  /**
   * @brief Report an scanner/parser error
   *
   * Replacement for yyerror() to get more info.
   * HACK ALERT: new_file() does not have yylloc initialized yet for
   * first file so create a string and init line number for it.
   */

void config_parse_error(YYLTYPE *yylloc_param,
			struct parser_state *st,
			char *format, ...)
{
	FILE *fp = st->err_type->fp;
	va_list arguments;
	char *filename = "<unknown file>";
	int linenum = 0;;

	if (fp == NULL)
		return;  /* no stream, no message */
	if (yylloc_param != NULL) {
	  filename = yylloc_param->filename;
	  linenum = yylloc_param->first_line;
	}
	va_start(arguments, format);
	config_error(fp, filename, linenum, format, arguments);
	va_end(arguments);
}

/* This is here because bison wants it declared.
 * We do not use it because we can't get around the API.
 * Use config_parse_error() instead.
 */

void gnfs_yyerror(YYLTYPE *yylloc_param,
		     void *yyscanner,
		     char *s){

  LogCrit(COMPONENT_CONFIG,
	  "Config file (%s:%d) error: %s\n",
	  yylloc_param->filename,
	  yylloc_param->first_line,
	  s);
}

/**
 * @brief Notes on parse tree linkage
 *
 * We use glist a little differently so beware.
 * Elsewhere in the server, we only glist_init() the head
 * and leave the 'node' members alone (both next and prev == NULL).
 * However, the parse FSM handles siblings as a list via the LR rules.
 * This means that while sub_nodes is the "head" of the list, it only
 * gets linked in when the rules add the already formed list is fully
 * parsed. Therefore, to make this all work, each node's 'node' member
 * gets a turn as the head which requires it to be glist_init()'d
 * contrary to what the rest of the code does.  The last node to play
 * 'head' is then the 'sub_nodes' member of the parent.
 */

/**
 *  Create a block item with the given content
 */

struct glist_head all_blocks;

void dump_all_blocks(void)
{
	struct glist_head *glh;
	struct config_node *node;

	int ix = 0;
	glist_for_each(glh, &all_blocks) {
		node = glist_entry(glh, struct config_node, blocks);
		printf("%s: ix: %d node blockname: %s\n",
			__func__, ix, node->u.nterm.name);
		++ix;
	}
}

struct config_node *config_block(char *blockname,
				 struct config_node *list,
				 YYLTYPE *yylloc_param,
				 struct parser_state *st)
{
	struct config_node *node;

	node = gsh_calloc(1, sizeof(struct config_node));
	if (node == NULL) {
		st->err_type->resource = true;
		return NULL;
	}
	glist_init(&node->node);
	glist_init(&node->blocks);
	node->u.nterm.name = blockname;
	node->filename = yylloc_param->filename;
	node->linenumber = yylloc_param->first_line;
	node->type = TYPE_BLOCK;
	glist_init(&node->u.nterm.sub_nodes);
	if (list != NULL) {
		glist_add_tail(&list->node, &node->u.nterm.sub_nodes);
		link_node(node);
	}
	glist_add_tail(&all_blocks, &node->blocks);
	return node;
}

/**
 * @brief Walk the subnode list and update all the sub-blocks in it
 * so we can find the root of the parse tree when we need it.
 */

void link_node(struct config_node *node)
{
	struct config_node *subnode;
	struct glist_head *ns;

	assert(node->type == TYPE_BLOCK ||
	       node->type == TYPE_ROOT);
	glist_for_each(ns, &node->u.nterm.sub_nodes) {
		subnode = glist_entry(ns, struct config_node, node);
		if (subnode->type == TYPE_BLOCK)
			subnode->u.nterm.parent = node;
	}
}

/**
 * @brief Link siblings together
 *
 */

struct config_node *link_sibling(struct config_node *first,
				 struct config_node *second)
{
	if (first == NULL) {
		return second;
	} else {
		if (second != NULL)
			glist_add_tail(&first->node, &second->node);
		return first;
	}
}

/**
 *  Create a term (value)
 */

struct config_node *config_term(char *opcode,
				char *varval,
				enum term_type type,
				YYLTYPE *yylloc_param,
				struct parser_state *st)
{
	struct config_node *node;

	node = gsh_calloc(1, sizeof(struct config_node));
	if (node == NULL) {
		st->err_type->resource = true;
		return NULL;
	}
	glist_init(&node->node);
	node->filename = yylloc_param->filename;
	node->linenumber = yylloc_param->first_line;
	node->type = TYPE_TERM;
	node->u.term.type = type;
	node->u.term.op_code = opcode;
	node->u.term.varvalue = varval;
	return node;
}

/**
 *  Create a statement node (key = list of terms)
 */

struct config_node *config_stmt(char *varname,
				struct config_node *exprlist,
				 YYLTYPE *yylloc_param,
				struct parser_state *st)
{
	struct config_node *node;

	node = gsh_calloc(1, sizeof(struct config_node));
	if (node == NULL) {
		st->err_type->resource = true;
		return NULL;
	}
	glist_init(&node->node);
	glist_init(&node->u.nterm.sub_nodes);
	node->filename = yylloc_param->filename;
	node->linenumber = yylloc_param->first_line;
	node->type = TYPE_STMT;
	node->u.nterm.name = varname;
	if (exprlist != NULL)
		glist_add_tail(&exprlist->node, &node->u.nterm.sub_nodes);
	return node;
}


int gnfs_yylex(YYSTYPE *yylval_param,
		  YYLTYPE *yylloc_param,
		  struct parser_state *st)
{
	return ganeshun_yylex(yylval_param,
			      yylloc_param,
			      st->scanner);

}
