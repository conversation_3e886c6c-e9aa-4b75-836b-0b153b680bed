/*
 * Copyright © 2012-2014, CohortFS, LLC.
 * Author: <PERSON> <<EMAIL>>
 *
 * contributeur : <PERSON> <<EMAIL>>
 *		  <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.	 See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @file   internal.c
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @date Wed Oct 22 13:24:33 2014
 *
 * @brief Internal definitions for the Idfs FSAL
 *
 * This file includes internal function definitions, constants, and
 * variable declarations used to impelment the Idfs FSAL, but not
 * exposed as part of the API.
 */

#include <sys/stat.h>
#ifdef IDFSFS_POSIX_ACL
#include <sys/acl.h>
#include <acl/libacl.h>
#endif				/* IDFSFS_POSIX_ACL */
#include <idfsfs/libidfsfs.h>
#include "fsal_types.h"
#include "fsal.h"
#include "fsal_convert.h"
#include "FSAL/fsal_commonlib.h"
#include "statx_compat.h"
#include "nfs_exports.h"
#include "internal.h"
#ifdef IDFSFS_POSIX_ACL
#include "posix_acls.h"
#endif				/* IDFSFS_POSIX_ACL */

#include "pwd.h"
#include "grp.h"


/**
 * @brief Construct a new filehandle
 *
 * This function constructs a new Idfs FSAL object handle and attaches
 * it to the export.  After this call the attributes have been filled
 * in and the handdle is up-to-date and usable.
 *
 * @param[in]  stx    idfs_statx data for the file
 * @param[in]  export Export on which the object lives
 * @param[out] obj    Object created
 *
 * @return 0 on success, negative error codes on failure.
 */
 
#define IDFS_ACL_ENTRY_SHOULD_SKIP(entry)	((entry.tag == 0) && (entry.perm == 0) && (entry.uid == 0) && (entry.gid == 0))
#define IDFS_ACL_ENTRY_SET_SKIP(entry)		(entry.tag = 0, entry.perm = 0, entry.uid = 0, entry.gid = 0)
#define CHECK_IF_IDFS_ACL_ENTRIES_SAME(entry1, entry2)	((entry1->flags == entry2.flags) &&(entry1->tag == entry2.tag) && (entry1->uid == entry2.uid) && (entry1->gid == entry2.gid))
#define IDFS_ACL_DEFAULT_VALUE 		0
#define CHECK_IF_IDFS_ACL_ENTRIES_SAME_V4(entry1, entry2)	((entry1->type == entry2->type) &&(entry1->flags == entry2->flags) &&(entry1->tag == entry2->tag) && (entry1->uid == entry2->uid) && (entry1->gid == entry2->gid))
#define IDFS_ACL_ENTRY_SHOULD_SKIP_V4(entry)	((entry->tag == 0) && (entry->perm == 0) && (entry->uid == 0) && (entry->type == 0) && (entry->gid == 0))

int get_groups_from_uid(gid_t uid, gid_t** gids, int* gids_count){
	int sgid_count = 32;
	int r = 0;
	struct passwd *result;
	struct passwd pwd;
	long bufsize = sysconf(_SC_GETPW_R_SIZE_MAX);
	if (bufsize == -1) {
		bufsize = 16384;
	}
	char* buf = (char*)malloc(bufsize);
	if(buf == NULL){
		return -1;
	}
	getpwuid_r(uid, &pwd, buf, bufsize, &result);
	if (result == NULL) {
		free(buf);
		LogWarn(COMPONENT_FSAL,"call getpwuid_r result = NULL, uid:%d.", uid);
		return -1;
	}

	gid_t* sgid_buf = (gid_t*)malloc(sgid_count * sizeof(gid_t));
	if (sgid_buf == NULL){
		free(buf);
		LogWarn(COMPONENT_FSAL,"sgid_buf malloc false, uid:%d.", uid);
		return -1;
	}

	while (1){
		r = getgrouplist(pwd.pw_name, pwd.pw_gid, sgid_buf, &sgid_count);
		if (r == -1){
			/* we need to resize the group list and try again*/
			void *_realloc = NULL;
			if ((_realloc = realloc(sgid_buf, sgid_count * sizeof(gid_t))) == NULL){
				free(sgid_buf);
				free(buf);
				LogWarn(COMPONENT_FSAL,"_realloc realloc false, uid:%d.", uid);
				return -1;
			}
			sgid_buf = (gid_t*)_realloc;
			continue;
		}
		break;
	}
	free(buf);
	*gids = sgid_buf;
	*gids_count = sgid_count;
	
	if(sgid_count > 0){
		int i = 0;
		for(; i < sgid_count; ++i){
			LogFullDebug(COMPONENT_FSAL,"uid: %d, gid[%d]:%d.", uid, i, sgid_buf[i]);
		}
	}else{
		LogWarn(COMPONENT_FSAL,"call getgrouplist err, uid: %u, count: %d.", uid, sgid_count);	
	}

	return sgid_count;
}

void construct_handle(const struct idfs_statx *stx, struct IdfsInode *i,
	struct idfs_export *export, struct idfs_handle **obj)
{
	/* Pointer to the handle under construction */
	struct idfs_handle *constructing = NULL;

	assert(i);

	constructing = gsh_calloc(1, sizeof(struct idfs_handle));

	constructing->key.hhdl.chk_ino = stx->stx_ino;
#ifdef IDFS_NOSNAP
	constructing->key.hhdl.chk_snap = stx->stx_dev;
#endif /* IDFS_NOSNAP */
	constructing->key.hhdl.chk_fscid = export->fscid;
	strncpy(constructing->key.hhdl.chk_fsid, nfs_param.core_param.gnfs_fh_fsid, strlen(nfs_param.core_param.gnfs_fh_fsid));
	LogFullDebug(COMPONENT_FSAL,"chk_fsid: len %lu str %s, gnfs_fh_fsid: len %lu str %s", 
		strlen(constructing->key.hhdl.chk_fsid), constructing->key.hhdl.chk_fsid, strlen(nfs_param.core_param.gnfs_fh_fsid), nfs_param.core_param.gnfs_fh_fsid);
	constructing->key.hhdl.chk_vision = FH_VERSION;
	if (nfs_param.core_param.enable_root_export == true) {
		extern struct idfs_export root_export;
		constructing->key.export_id = root_export.export.export_id;
		LogFullDebug(COMPONENT_FSAL, "Using root export_id=%u instead of current export_id=%u for cache key", root_export.export.export_id, export->export.export_id);
	} else {
		constructing->key.export_id = export->export.export_id;
	}
	constructing->i = i;
	constructing->up_ops = export->export.up_ops;

	fsal_obj_handle_init(&constructing->handle, &export->export,
			     posix2fsal_type(stx->stx_mode));
	constructing->handle.obj_ops = &IdfsFSM.handle_ops;
	constructing->handle.fsid = posix2fsal_fsid(stx->stx_dev);
	constructing->handle.fileid = stx->stx_ino;

	*obj = constructing;
}

/**
 * @brief Release all resrouces for a handle
 *
 * @param[in] obj Handle to release
 */

void deconstruct_handle(struct idfs_handle *obj)
{
	struct idfs_export *export =
		container_of(op_ctx->fsal_export, struct idfs_export, export);

	if (nfs_param.core_param.enable_root_export == true) {
		extern struct idfs_export root_export;
		assert(root_export.export.export_id == obj->key.export_id);
	} else {
		assert(op_ctx->fsal_export->export_id == obj->key.export_id);
	}
	
	idfs_ll_put(export->cmount, obj->i);
	fsal_obj_handle_fini(&obj->handle);
	gsh_free(obj);
}

unsigned int
attrmask2idfs_want(attrmask_t mask)
{
	unsigned int want = 0;

	if (mask & ATTR_MODE)
		want |= IDFS_STATX_MODE;
	if (mask & ATTR_OWNER)
		want |= IDFS_STATX_UID;
	if (mask & ATTR_GROUP)
		want |= IDFS_STATX_GID;
	if (mask & ATTR_SIZE)
		want |= IDFS_STATX_SIZE;
	if (mask & ATTR_NUMLINKS)
		want |= IDFS_STATX_NLINK;
	if (mask & ATTR_SPACEUSED)
		want |= IDFS_STATX_BLOCKS;
	if (mask & ATTR_ATIME)
		want |= IDFS_STATX_ATIME;
	if (mask & ATTR_CTIME)
		want |= IDFS_STATX_CTIME;
	if (mask & ATTR_MTIME)
		want |= IDFS_STATX_MTIME;
	if (mask & ATTR_CREATION)
		want |= IDFS_STATX_BTIME;
	if (mask & ATTR_CHANGE)
		want |= IDFS_STATX_VERSION;

	return want;
}

void idfs2fsal_attributes(const struct idfs_statx *stx,
			  struct fsal_attrlist *fsalattr)
{
	/* These are always considered to be available */
	fsalattr->valid_mask |= ATTR_TYPE|ATTR_FSID|ATTR_RAWDEV|ATTR_FILEID;
	fsalattr->supported = IDFS_SUPPORTED_ATTRS;
	fsalattr->type = posix2fsal_type(stx->stx_mode);
	fsalattr->rawdev = posix2fsal_devt(stx->stx_rdev);
	fsalattr->fsid = posix2fsal_fsid(stx->stx_dev);
	fsalattr->fileid = stx->stx_ino;

	/* Disable seclabels if not enabled in config */
	if (!op_ctx_export_has_option(EXPORT_OPTION_SECLABEL_SET))
		fsalattr->supported &= ~ATTR4_SEC_LABEL;

	if (stx->stx_mask & IDFS_STATX_MODE) {
		fsalattr->valid_mask |= ATTR_MODE;
		fsalattr->mode = unix2fsal_mode(stx->stx_mode);
	}
	if (stx->stx_mask & IDFS_STATX_UID) {
		fsalattr->valid_mask |= ATTR_OWNER;
		fsalattr->owner = stx->stx_uid;
	}
	if (stx->stx_mask & IDFS_STATX_GID) {
		fsalattr->valid_mask |= ATTR_GROUP;
		fsalattr->group = stx->stx_gid;
	}
	if (stx->stx_mask & IDFS_STATX_SIZE) {
		fsalattr->valid_mask |= ATTR_SIZE;
		fsalattr->filesize = stx->stx_size;
	}
	if (stx->stx_mask & IDFS_STATX_NLINK) {
		fsalattr->valid_mask |= ATTR_NUMLINKS;
		fsalattr->numlinks = stx->stx_nlink;
	}

	if (stx->stx_mask & IDFS_STATX_BLOCKS) {
		fsalattr->valid_mask |= ATTR_SPACEUSED;
		fsalattr->spaceused = stx->stx_blocks * S_BLKSIZE;
	}

	/* Use full timer resolution */
	if (stx->stx_mask & IDFS_STATX_ATIME) {
		fsalattr->valid_mask |= ATTR_ATIME;
		fsalattr->atime = stx->stx_atime;
	}
	if (stx->stx_mask & IDFS_STATX_CTIME) {
		fsalattr->valid_mask |= ATTR_CTIME;
		fsalattr->ctime = stx->stx_ctime;
	}
	if (stx->stx_mask & IDFS_STATX_MTIME) {
		fsalattr->valid_mask |= ATTR_MTIME;
		fsalattr->mtime = stx->stx_mtime;
	}
	if (stx->stx_mask & IDFS_STATX_BTIME) {
		fsalattr->valid_mask |= ATTR_CREATION;
		fsalattr->creation = stx->stx_btime;
	}

	if (stx->stx_mask & IDFS_STATX_VERSION) {
		fsalattr->valid_mask |= ATTR_CHANGE;
		fsalattr->change = stx->stx_version;
	}
}
uint32_t posix_perm_to_idfs_perm( uint16_t posix_perm, bool use_dv, bool directory)
{
	uint32_t idfs_perm = 0;

	/*get rid of 'd' and 'v',just use 'rwx'*/
	if (!use_dv){
		if (!directory){
			idfs_perm |= ((posix_perm & ACL_POSIX_READ) == ACL_POSIX_READ) ? IDFS_ACL_FILE_READ : 0;
			idfs_perm |= ((posix_perm & ACL_POSIX_WRITE) == ACL_POSIX_WRITE) ? IDFS_ACL_FILE_WRITE : 0;
			idfs_perm |= ((posix_perm & ACL_POSIX_EXECUTE) == ACL_POSIX_EXECUTE) ? IDFS_ACL_FILE_EXECUTE : 0;
			if((posix_perm & ACL_POSIX_RWX) == ACL_POSIX_RWX)
				idfs_perm |= IDFS_ACL_FULL_CONTROL;

		}else{
			idfs_perm |= ((posix_perm & ACL_POSIX_READ) == ACL_POSIX_READ) ? IDFS_ACL_DIR_READ : 0;
			idfs_perm |= ((posix_perm & ACL_POSIX_WRITE) == ACL_POSIX_WRITE) ? IDFS_ACL_DIR_WRITE : 0;
			idfs_perm |= ((posix_perm & ACL_POSIX_EXECUTE) == ACL_POSIX_EXECUTE) ? IDFS_ACL_DIR_EXECUTE : 0;
			if((posix_perm & ACL_POSIX_RWX) == ACL_POSIX_RWX)
				idfs_perm |= IDFS_ACL_FULL_CONTROL;

		}
	}
	return idfs_perm;
}

uint16_t idfs_perm_to_posix_perm( unsigned int idfs_perm, bool use_dv, bool directory)
{
	uint16_t posix_perm = 0;
	/*get rid of 'd' and 'v',just use 'rwx'*/
	if (!use_dv){
		if (!directory){
			posix_perm |= ((idfs_perm & IDFS_ACL_FILE_READ) == IDFS_ACL_FILE_READ) ? ACL_POSIX_READ : 0;
			posix_perm |= ((idfs_perm & IDFS_ACL_FILE_WRITE) == IDFS_ACL_FILE_WRITE) ? ACL_POSIX_WRITE : 0;
			posix_perm |= ((idfs_perm & IDFS_ACL_FILE_EXECUTE) == IDFS_ACL_FILE_EXECUTE) ? ACL_POSIX_EXECUTE : 0;
			
		}else{
			posix_perm |= ((idfs_perm & IDFS_ACL_DIR_READ) == IDFS_ACL_DIR_READ) ? ACL_POSIX_READ : 0;
			posix_perm |= ((idfs_perm & IDFS_ACL_DIR_WRITE) == IDFS_ACL_DIR_WRITE) ? ACL_POSIX_WRITE : 0;
			posix_perm |= ((idfs_perm & IDFS_ACL_DIR_EXECUTE) == IDFS_ACL_DIR_EXECUTE) ? ACL_POSIX_EXECUTE : 0;

		}
	}
	return posix_perm;
}

uint32_t idfs_acl_2_mode(idfs_acl_t *idfs_acl, bool is_dir)
{
	int len = 0;
	uint32_t mode = 0;
	uint32_t u_rwx = 0;
	uint32_t g_rwx = 0;
	uint32_t o_rwx = 0;
	uint32_t m_rwx = 0;
	bool u_mask_o = false;
	for (len = 0;
		len < idfs_acl->dacl_ace_count ; len++) {
			if(idfs_acl->entry[len].type == IDFS_ACE_TYPE_ACCESS_ALLOWED && ACL_ECTRY_IS_ACCESS(idfs_acl->entry[len].flags)){	
				switch (idfs_acl->entry[len].tag) {
				case IDFS_ACL_USER_OBJ:
					u_rwx |= idfs_perm_to_posix_perm(idfs_acl->entry[len].perm, false, is_dir);
					break;
				case IDFS_ACL_USER:
					u_mask_o = true;
					break;
				case IDFS_ACL_GROUP_OBJ:
					g_rwx |= idfs_perm_to_posix_perm(idfs_acl->entry[len].perm, false, is_dir);
					break;
				case IDFS_ACL_GROUP:
					u_mask_o = true;
					break;
				case IDFS_ACL_MASK:
					m_rwx |= idfs_perm_to_posix_perm(idfs_acl->entry[len].perm, false, is_dir);
					break;
				case IDFS_ACL_OTHER:
					o_rwx |= idfs_perm_to_posix_perm(idfs_acl->entry[len].perm, false, is_dir);
					break;
				
				default:
					LogMajor(COMPONENT_IDFS_ACL, "Undefined ACL type");
					break;
				}
			}
		
	}
	if(u_mask_o){
		mode |= u_rwx;
		mode = mode << 3;
		mode |= m_rwx;
		mode = mode << 3;
		mode |= o_rwx;
	}else{
		mode |= u_rwx;
		mode = mode << 3;
		mode |= g_rwx;
		mode = mode << 3;
		mode |= o_rwx;
	}
	return mode;
}

int idfs_acl_printf(idfs_acl_t *idfs_acl)
{

	int ret = 1;
	int i =0;
	if(!idfs_acl){
		LogDebug(COMPONENT_IDFS_ACL,"idfs_acl_printf, idfs_acl is null");
		return -1;
	}
	for (i = 0; i < idfs_acl->dacl_ace_count; i++) {//idfs_acl->dacl_ace_count
		LogDebug(COMPONENT_IDFS_ACL,
		"len:%d,  group:%d, owner:%d, flag:%#x, tag:%#x, perm:%#x, type:%#x, uid:%d, gid:%d",
		i, idfs_acl->group, idfs_acl->owner,idfs_acl->entry[i].flags, idfs_acl->entry[i].tag, idfs_acl->entry[i].perm,idfs_acl->entry[i].type,idfs_acl->entry[i].uid,idfs_acl->entry[i].gid);
	}
	return ret;
}

int ckeck_idfs_acl(idfs_acl_t *idfs_acl, int size)
{
	int real_size = 0;
	
	real_size = sizeof(idfs_acl_t) + (idfs_acl->dacl_ace_count + idfs_acl->sacl_ace_count) * sizeof(idfs_acl_entry_t);
	LogDebug(COMPONENT_IDFS_ACL, "ckeck_idfs_acl,size:%d, real_size:%d", size, real_size);
	if (real_size == size){
		return real_size;
	}else{
		return -1;
	}
}

acl_t idfs_acl_2_posix_acl(idfs_acl_t *idfs_acl, bool is_dir, acl_type_t type)
{
	
	int count;
	acl_t tem_acl = NULL;

	int ret = 0;	
	int end = 0;
	acl_entry_t acl_entry;
	acl_tag_t tag;
	acl_permset_t permset;
	uid_t uid;
	gid_t gid;
	idfs_acl_entry_t ea_entry ;

	if ((type != ACL_TYPE_ACCESS)  & (type != ACL_TYPE_DEFAULT)) {
		LogMajor(COMPONENT_IDFS_ACL, "type is error, type:%#x,ACL_TYPE_ACCESS:%#x,ACL_TYPE_DEFAULT:%#x,",
			type,ACL_TYPE_ACCESS,ACL_TYPE_DEFAULT);
		goto out;
	} 
	if (!idfs_acl) {
		LogDebug(COMPONENT_IDFS_ACL,
			"idfs_acl_2_posix_acl :idfs_acl is null");
		return NULL;
	}

	count = idfs_acl->dacl_ace_count;
	tem_acl = acl_init(count);

	if (!tem_acl) {
		LogMajor(COMPONENT_IDFS_ACL,
			"Failed to tem_acl INIT: count = %d, type:%#x",count ,type);
		return NULL;
	}

	for (end = 0; end < count; end++) {
		ea_entry = idfs_acl->entry[end];
			
		if ((ACL_ECTRY_IS_ACCESS(ea_entry.flags) && (type == ACL_TYPE_ACCESS)) || 
			(ACL_ECTRY_IS_DEFAULT(ea_entry.flags) && (type == ACL_TYPE_DEFAULT))){

			ret = acl_create_entry(&tem_acl, &acl_entry);
			if (ret) {
				LogMajor(COMPONENT_IDFS_ACL, "Failed to create acl entry");
				goto out;
			}
			tag = ea_entry.tag;
			ret = acl_get_permset(acl_entry, &permset);
			if (ret) {
				LogWarn(COMPONENT_IDFS_ACL, "Failed to get acl permset");
				goto out;
			}
			uint16_t posix_perm = 0;
			posix_perm = idfs_perm_to_posix_perm(ea_entry.perm, false, is_dir);
			ret = acl_add_perm(permset, posix_perm);
			if (ret) {
				LogWarn(COMPONENT_IDFS_ACL, "Failed to add acl permission %d",ret);
				goto out;
			}

			switch (tag) {
			case IDFS_ACL_USER_OBJ:
				ret = acl_set_tag_type(acl_entry, ACL_USER_OBJ);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set acl tag type ACL_USER_OBJ");
					goto out;
				}
				break;

			case IDFS_ACL_GROUP_OBJ:
				ret = acl_set_tag_type(acl_entry, ACL_GROUP_OBJ);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set acl tag type ACL_GROUP_OBJ");
					goto out;
				}
				break;
				
			case IDFS_ACL_MASK:
				ret = acl_set_tag_type(acl_entry, ACL_MASK);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set acl tag type ACL_MASK");
					goto out;
				}
				break;	
				
			case IDFS_ACL_OTHER:
				ret = acl_set_tag_type(acl_entry, ACL_OTHER);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set acl tag type ACL_OTHER");
					goto out;
				}
				break;

			case IDFS_ACL_USER:
				ret = acl_set_tag_type(acl_entry, ACL_USER);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set acl tag type ACL_USER");
					goto out;
				}
				uid = le32toh(ea_entry.uid);
				ret = acl_set_qualifier(acl_entry, &uid);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set uid");
					goto out;
				}
				break;

			case IDFS_ACL_GROUP:
				ret = acl_set_tag_type(acl_entry, ACL_GROUP);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set acl tag type ACL_GROUP");
					goto out;
				}
				gid = le32toh(ea_entry.gid);
				ret = acl_set_qualifier(acl_entry, &gid);
				if (ret) {
					LogMajor(COMPONENT_IDFS_ACL, "Failed to set gid");
					goto out;
				}
				break;

			default:
				goto out;
			}
		}
	}
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		char *acl_str = NULL;
		acl_str = acl_to_any_text(tem_acl, NULL, ',',
				TEXT_ABBREVIATE | TEXT_NUMERIC_IDS);
		LogDebug(COMPONENT_IDFS_ACL, "idfs_acl_2_posix_acl is over, posix_acl = %s, type:%#x",acl_str, type);
		if (acl_str) {
			acl_free(acl_str);
			acl_str = NULL;
		}

	}
out:
	return tem_acl;
}
int posix_acl_2_idfs_acl(acl_t access_acl, acl_t defalut_acl, idfs_acl_t **pp_idfs_acl, 
	size_t size, bool is_dir, acl_type_t type, struct fsal_attrlist *attrs)
{
	acl_entry_t acl_entry;
	acl_tag_t tag;
	acl_permset_t permset;
	int real_size = 0, access_count = 0, defalut_count = 0, entry_id = 0, k = 0;
	int ret = 0;
	 
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		char *acl_str = NULL;
		acl_str = acl_to_any_text(access_acl, NULL, ',',
				TEXT_ABBREVIATE | TEXT_NUMERIC_IDS);
		LogDebug(COMPONENT_IDFS_ACL, "posix_acl_2_idfs_acl:posix access_acl = %s ", acl_str);
		if (acl_str) {
			acl_free(acl_str);
			acl_str = NULL;
		}
		acl_str = acl_to_any_text(defalut_acl, NULL, ',',
				TEXT_ABBREVIATE | TEXT_NUMERIC_IDS);
		LogDebug(COMPONENT_IDFS_ACL, "posix_acl_2_idfs_acl:posix defalut_acl = %s ", acl_str);
		if (acl_str) {
			acl_free(acl_str);
			acl_str = NULL;
		}
	}
	if (access_acl != NULL){
		access_count = acl_entries(access_acl);
	}
	
	if (defalut_acl != NULL){
		defalut_count = acl_entries(defalut_acl);
	}

	idfs_acl_t *idfs_acl = NULL;
	real_size = sizeof(idfs_acl_t) + (access_count + defalut_count) * sizeof(idfs_acl_entry_t);
	idfs_acl = (idfs_acl_t *)malloc(real_size);
	if (!idfs_acl){
		LogEvent(COMPONENT_IDFS_ACL,"malloc failed for idfs_acl");
		return -1;
	}
	memset(idfs_acl, 0, real_size);

	LogFullDebug(COMPONENT_IDFS_ACL, "posix_acl_2_idfs_acl ,real_size = %d, access_count = %d, defalut_count = %d", 
			(int)real_size, access_count, defalut_count);

	idfs_acl->dacl_ace_count = access_count + defalut_count;
	entry_id = 0;

	idfs_acl->acl_ver = IDFS_ACL_VERSION_V11;
	idfs_acl->acl_type = IDFS_ACL_TYPE_POSIX;
	idfs_acl->sd_ver = 0;
	idfs_acl->ntacl_ver = 0;
	idfs_acl->ctrl_flags = 0;
	idfs_acl->owner = attrs->owner;
	idfs_acl->group = attrs->group;

	/*access_acl*/
	for (k = ACL_FIRST_ENTRY; k < acl_entries(access_acl); k = ACL_NEXT_ENTRY){
		ret = acl_get_entry(access_acl, k, &acl_entry);
		if (ret == 0 || ret == -1) {
			LogDebug(COMPONENT_IDFS_ACL,
				"No more ACL entries remaining");
			break;
		}
		if (acl_get_tag_type(acl_entry, &tag) == -1) {
			LogWarn(COMPONENT_IDFS_ACL, "No entry tag for ACL Entry");
			continue;
		}	
		ret = acl_get_permset(acl_entry, &permset);
		if (ret) {
			LogWarn(COMPONENT_IDFS_ACL,
			"Cannot retrieve permission set for the ACL Entry");
			continue;
		}

		/*type allow or deny*/
		idfs_acl->entry[entry_id].type = IDFS_ACE_TYPE_ACCESS_ALLOWED;
		idfs_acl->entry[entry_id].perm = 0;

		/*access or default*/
		idfs_acl->entry[entry_id].flags = 0;	

		if (acl_get_perm(permset, ACL_READ))
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_READ, false, is_dir);
		if (acl_get_perm(permset, ACL_WRITE))
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_WRITE, false, is_dir);
		if (acl_get_perm(permset, ACL_EXECUTE))
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_EXECUTE, false, is_dir);
		if ((acl_get_perm(permset, ACL_READ)) & (acl_get_perm(permset, ACL_WRITE)) & (acl_get_perm(permset, ACL_EXECUTE)) ){
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_RWX, false, is_dir);
		}
		
		switch (tag) {
		case ACL_USER_OBJ:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_USER_OBJ;
			idfs_acl->entry[entry_id].uid = attrs->owner;
			break;
		case ACL_USER:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_USER;
				idfs_acl->entry[entry_id].uid =
				htole32(posix_acl_get_uid(acl_entry));
			break;
		case ACL_GROUP_OBJ:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_GROUP_OBJ;
			idfs_acl->entry[entry_id].uid = attrs->group;
			break;
		case ACL_GROUP:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_GROUP;
			idfs_acl->entry[entry_id].gid =
				htole32(posix_acl_get_gid(acl_entry));

			break;
		case ACL_MASK:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_MASK;
			idfs_acl->entry[entry_id].uid = (__le32)-1;
			break;
		case ACL_OTHER:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_OTHER;
			idfs_acl->entry[entry_id].uid = (__le32)-1;
			break;
		
		default:
			LogMajor(COMPONENT_IDFS_ACL, "Undefined ACL type");
		}
		entry_id +=1;
	}

	/*defalut_acl*/
	for (k = ACL_FIRST_ENTRY; k < acl_entries(defalut_acl); k = ACL_NEXT_ENTRY){
		ret = acl_get_entry(defalut_acl, k, &acl_entry);
		if (ret == 0 || ret == -1) {
			LogDebug(COMPONENT_IDFS_ACL,
				"No more ACL entries remaining");
			break;
		}

		if (acl_get_tag_type(acl_entry, &tag) == -1) {
			LogWarn(COMPONENT_IDFS_ACL, "No entry tag for ACL Entry");
			continue;
		}	
		ret = acl_get_permset(acl_entry, &permset);
		if (ret) {
			LogWarn(COMPONENT_IDFS_ACL,
			"Cannot retrieve permission set for the ACL Entry");
			continue;
		}
		/*type allow or deny*/
		idfs_acl->entry[entry_id].type = IDFS_ACE_TYPE_ACCESS_ALLOWED;
		
		idfs_acl->entry[entry_id].perm = 0;

		/*access or default*/
		idfs_acl->entry[entry_id].flags = (IDFS_ACE_FILE_INHERIT|IDFS_ACE_SUBDIR_INHERIT|IDFS_ACE_INHERIT_ONLY);

		if (acl_get_perm(permset, ACL_READ))
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_READ, false, is_dir);
		if (acl_get_perm(permset, ACL_WRITE))
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_WRITE, false, is_dir);
		if (acl_get_perm(permset, ACL_EXECUTE))
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_EXECUTE, false, is_dir);
		if ((acl_get_perm(permset, ACL_READ)) & (acl_get_perm(permset, ACL_WRITE)) & (acl_get_perm(permset, ACL_EXECUTE)) ){
			idfs_acl->entry[entry_id].perm |= posix_perm_to_idfs_perm(ACL_POSIX_RWX, false, is_dir);
		}

		switch (tag) {
		case ACL_USER_OBJ:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_USER_OBJ;
			idfs_acl->entry[entry_id].uid = attrs->owner;
			break;
		case ACL_USER:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_USER;
				idfs_acl->entry[entry_id].uid =
				htole32(posix_acl_get_uid(acl_entry));
			break;
		case ACL_GROUP_OBJ:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_GROUP_OBJ;
			idfs_acl->entry[entry_id].uid = attrs->group;
			break;
		case ACL_GROUP:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_GROUP;
			idfs_acl->entry[entry_id].gid =
				htole32(posix_acl_get_gid(acl_entry));

			break;
		case ACL_MASK:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_MASK;
			idfs_acl->entry[entry_id].uid = (__le32)-1;
			break;
		case ACL_OTHER:
			idfs_acl->entry[entry_id].tag = IDFS_ACL_OTHER;
			idfs_acl->entry[entry_id].uid = (__le32)-1;
			break;
		
		default:
			LogMajor(COMPONENT_IDFS_ACL, "Undefined ACL type");
		}
		entry_id +=1;
	}

	*pp_idfs_acl = idfs_acl;
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		LogDebug(COMPONENT_IDFS_ACL, "posix_acl_2_idfs_acl is over. printf idfs_acl ");
		entry_id = idfs_acl_printf(idfs_acl);
	}
	return real_size;
}

int idfs_acl_from_mode(idfs_acl_t **pp_idfs_acl, acl_type_t type, bool is_dir, struct fsal_attrlist *attrs){
	int real_size =0;
	uint32_t u_rwx = 0;
	uint32_t g_rwx = 0;
	uint32_t o_rwx = 0;
	uint32_t mode = attrs->mode;
	o_rwx = mode & 07;
	g_rwx = (mode >> 3) & 07;
	u_rwx = (mode >> 6)  & 07;

	LogDebug(COMPONENT_IDFS_ACL, "u_rwx:%#o, g_rwx:%#o, o_rwx:%#o, mode:%#o, attrs->mode:%#o", u_rwx, g_rwx, o_rwx, mode, attrs->mode);

	idfs_acl_t *p_idfs_acl = NULL;
	real_size = sizeof(idfs_acl_t) + 3 * sizeof(idfs_acl_entry_t);
	p_idfs_acl = (idfs_acl_t *)malloc(real_size);
        if (!p_idfs_acl){
                LogEvent(COMPONENT_IDFS_ACL,"malloc failed for p_idfs_acl");
                return -1;
        }
	memset(p_idfs_acl, 0, real_size);

	p_idfs_acl->acl_ver = IDFS_ACL_VERSION_V11;
	p_idfs_acl->sd_ver = 0;
	p_idfs_acl->ntacl_ver = 0;
	p_idfs_acl->ctrl_flags = 0;
	p_idfs_acl->owner = attrs->owner;
	p_idfs_acl->group = attrs->group;
	
	p_idfs_acl->dacl_ace_count = 3;
	p_idfs_acl->entry[0].flags = 0;
	p_idfs_acl->entry[0].uid = attrs->owner;
	p_idfs_acl->entry[0].perm = posix_perm_to_idfs_perm(u_rwx, false, is_dir);;
	p_idfs_acl->entry[0].tag = IDFS_ACL_USER_OBJ;
	p_idfs_acl->entry[0].type = IDFS_ACE_TYPE_ACCESS_ALLOWED;

	p_idfs_acl->entry[1].flags = 0;
	p_idfs_acl->entry[1].gid = attrs->group;
	p_idfs_acl->entry[1].perm = posix_perm_to_idfs_perm(g_rwx, false, is_dir);;
	p_idfs_acl->entry[1].tag = IDFS_ACL_GROUP_OBJ;
	p_idfs_acl->entry[1].type = IDFS_ACE_TYPE_ACCESS_ALLOWED;

	p_idfs_acl->entry[2].flags = 0;
	p_idfs_acl->entry[2].gid = -1;
	p_idfs_acl->entry[2].perm = posix_perm_to_idfs_perm(o_rwx, false, is_dir);;
	p_idfs_acl->entry[2].tag = IDFS_ACL_OTHER;
	p_idfs_acl->entry[2].type = IDFS_ACE_TYPE_ACCESS_ALLOWED;
	p_idfs_acl->entry[2].uid = -1;

	*pp_idfs_acl = p_idfs_acl;
	return real_size;

}

int mode_flash_acl(struct idfs_export *export, struct idfs_handle *objhandle,
	uint32_t mode, bool is_dir){

	int rc = 0, size = 0;
	char *value = NULL;
	idfs_acl_t *idfs_acl = NULL;
	size = fsal_idfs_ll_getxattr(export->cmount, objhandle->i, SYSTEM_IDFS_keyname,
				NULL, 0, &op_ctx->creds);
	if (size <= 0) {
		LogDebug(COMPONENT_IDFS_ACL, "getxattr returned %d", size);
		return 0;
	}

	value = gsh_malloc(size);
	if (!value){
		LogEvent(COMPONENT_IDFS_ACL,"malloc failed for value");
		return -1;
	}
	/* Read extended attribute's value */
	rc = fsal_idfs_ll_getxattr(export->cmount, objhandle->i, SYSTEM_IDFS_keyname,
				value, size, &op_ctx->creds);
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "getxattr returned %d", rc);
		if (rc == -ENODATA) {
			rc = 0;
		}
		goto out;
	}


	idfs_acl = (idfs_acl_t *)value;

	int i =0;
	uint32_t u_rwx = 0;
	uint32_t m_rwx = 0;
	uint32_t o_rwx = 0;
	o_rwx = mode & 07;
	m_rwx = (mode >> 3) & 07;
	u_rwx = (mode >> 6)  & 07;
	LogDebug(COMPONENT_IDFS_ACL, "u_rwx:%#o, g_rwx:%#o, o_rwx:%#o, mode:%#o", u_rwx, m_rwx, o_rwx, mode);
	
	for (i = 0; i < idfs_acl->dacl_ace_count; i++) {
		if(idfs_acl->entry[i].type == IDFS_ACE_TYPE_ACCESS_ALLOWED && ACL_ECTRY_IS_ACCESS(idfs_acl->entry[i].flags)){
			switch (idfs_acl->entry[i].tag) {
				case IDFS_ACL_USER_OBJ:
					idfs_acl->entry[i].perm = posix_perm_to_idfs_perm(u_rwx, false,  is_dir);
					break;

				case IDFS_ACL_GROUP_OBJ:
					break;
					
				case IDFS_ACL_OTHER:
					idfs_acl->entry[i].perm = posix_perm_to_idfs_perm( o_rwx, false, is_dir);
					break;
					
				case IDFS_ACL_MASK:
					idfs_acl->entry[i].perm = posix_perm_to_idfs_perm( m_rwx, false, is_dir);
					break;	
				
				case IDFS_ACL_USER:
					break;

				case IDFS_ACL_GROUP:
					break;
				default:
					break;
			}
		}

	}
	
	size = sizeof(idfs_acl_t) + idfs_acl->dacl_ace_count * sizeof(idfs_acl_entry_t);
	rc = fsal_idfs_ll_setxattr(export->cmount, objhandle->i,
				SYSTEM_IDFS_keyname, (char *)idfs_acl, size, 0, &op_ctx->creds);
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "failed to fsal_idfs_ll_setxattr: %d", rc);
		goto out;
	}
out:
	if(value){
		gsh_free(value);
		value = NULL;
        }
	return rc;

}

void copy_entry1_2_entry2(idfs_acl_entry_t *entry1, idfs_acl_entry_t *entry2, uint32_t set_perm){

	entry1->type = entry2->type;			
	entry1->flags = entry2->flags;			
	entry1->tag = entry2->tag; 			
	entry1->reserved = entry2->reserved;			
	entry1->uid = entry2->uid;			
	entry1->gid = entry2->gid;			
	entry1->perm = set_perm;
}

int merge_allow_deny_acl(idfs_acl_t *idfs_acl, acl_type_t type, idfs_acl_t **pp_idfs_acl){

	int size = 0;
	int i =0, j=0;
	int rc = 0 ;
	int allow_acl_count = 0;
	int deny_acl_count = 0;
	int merge_acl_count = 0;
	idfs_acl_entry_t *ea_entry = NULL;
	idfs_acl_entry_t *allow_idfs_acl = NULL;
	size = idfs_acl->dacl_ace_count * sizeof(idfs_acl_entry_t);
	allow_idfs_acl = (idfs_acl_entry_t *)malloc(size);
	if (!allow_idfs_acl){
		LogEvent(COMPONENT_IDFS_ACL,"malloc failed for allow_idfs_acl");
		rc = -1;
		goto out;

	}
	memset(allow_idfs_acl, 0, size);

	idfs_acl_entry_t *deny_idfs_acl = NULL;
	size = idfs_acl->dacl_ace_count * sizeof(idfs_acl_entry_t);
	deny_idfs_acl = (idfs_acl_entry_t *)malloc(size);
	if (!deny_idfs_acl){
		LogEvent(COMPONENT_IDFS_ACL,"malloc failed for deny_idfs_acl");
		rc = -1;
		goto out;
	}
	memset(deny_idfs_acl, 0, size);

	idfs_acl_entry_t *merge_idfs_acl = NULL;

	/* get allow_idfs_acl and  deny_idfs_acl, drop unmetched items*/
	for (i = 0; i < idfs_acl->dacl_ace_count; i++) {
		ea_entry = &idfs_acl->entry[i];
		LogFullDebug(COMPONENT_IDFS_ACL,
		"all the entries:flags:%#x, tag:%#x, perm:%#x, type:%#x, uid:%d, gid:%d,type:%#x, ACL_TYPE_ACCESS:%#x,ACL_TYPE_DEFAULT:%#x,",
		ea_entry->flags, ea_entry->tag, ea_entry->perm, ea_entry->type, ea_entry->uid, ea_entry->gid, type,ACL_TYPE_ACCESS,ACL_TYPE_DEFAULT);

		if ((ACL_ECTRY_IS_ACCESS(ea_entry->flags) && (type == ACL_TYPE_ACCESS)) || (ACL_ECTRY_IS_DEFAULT(ea_entry->flags) && (type == ACL_TYPE_DEFAULT))) {
			LogFullDebug(COMPONENT_IDFS_ACL,
			"keep the entry:flag:%#x, tag:%#x, perm:%#x, type:%#x, uid:%d, gid:%d,type:%#x",
			ea_entry->flags, ea_entry->tag, ea_entry->perm, ea_entry->type, ea_entry->uid, ea_entry->gid, type);
			switch (ea_entry->type) {
				case IDFS_ACE_TYPE_ACCESS_ALLOWED:
					copy_entry1_2_entry2(&allow_idfs_acl[allow_acl_count], ea_entry, ea_entry->perm);
					allow_acl_count++;
					break;
				case IDFS_ACE_TYPE_ACCESS_DENIED:
					copy_entry1_2_entry2(&deny_idfs_acl[deny_acl_count], ea_entry, ea_entry->perm);
					deny_acl_count++;
					break;
			}
			
		}


	}
	LogFullDebug(COMPONENT_IDFS_ACL, "allow_acl_count:%d, deny_acl_count:%d, idfs_acl->dacl_ace_count:%d", allow_acl_count, deny_acl_count, idfs_acl->dacl_ace_count);
	idfs_acl_t *p_idfs_acl = NULL;
	if((allow_acl_count == 0) && (deny_acl_count == 0)){
		rc = 0;
		goto out;
	}

	size = sizeof(idfs_acl_t) + (deny_acl_count + allow_acl_count) * sizeof(idfs_acl_entry_t);
	p_idfs_acl = (idfs_acl_t *)malloc(size);
	if (!p_idfs_acl){
		LogEvent(COMPONENT_IDFS_ACL,"malloc failed for p_idfs_acl");
		rc = -1;
		goto out;
	}
	memset(p_idfs_acl, 0, size);

	for (i = 0; i < allow_acl_count; i++) {
		merge_idfs_acl = &p_idfs_acl->entry[merge_acl_count];
		if (IDFS_ACL_ENTRY_SHOULD_SKIP(allow_idfs_acl[i]))
			continue;

		copy_entry1_2_entry2(merge_idfs_acl, &allow_idfs_acl[i], allow_idfs_acl[i].perm);
		/* get entry all collection in acl_allow */
		for (j = i + 1; j < allow_acl_count; j++) {
			if (CHECK_IF_IDFS_ACL_ENTRIES_SAME(merge_idfs_acl, allow_idfs_acl[j])) {
				merge_idfs_acl->perm |= allow_idfs_acl[j].perm;
				IDFS_ACL_ENTRY_SET_SKIP(allow_idfs_acl[j]);
			
			}
		}

		/* get entry all collection in acl_denied */
		for (j = 0; j < deny_acl_count; j++) {
			if (CHECK_IF_IDFS_ACL_ENTRIES_SAME(merge_idfs_acl, deny_idfs_acl[j])) {
				merge_idfs_acl->perm &= ~deny_idfs_acl[j].perm;
				IDFS_ACL_ENTRY_SET_SKIP(deny_idfs_acl[j]);
			}
		}
		merge_acl_count++;
	}

	for (i = 0; i < deny_acl_count; i++) {
		merge_idfs_acl = &p_idfs_acl->entry[merge_acl_count];
		/* this entry has handled, skip */
		if (IDFS_ACL_ENTRY_SHOULD_SKIP(deny_idfs_acl[i]))
			continue;

		copy_entry1_2_entry2(merge_idfs_acl, &deny_idfs_acl[i], IDFS_ACL_DEFAULT_VALUE);
		merge_acl_count++;
	}
	p_idfs_acl->dacl_ace_count = merge_acl_count ;
	rc = 1;
	*pp_idfs_acl = p_idfs_acl;
	LogDebug(COMPONENT_IDFS_ACL, "merge_allow_deny_acl ,merge_acl_count:%d, p_idfs_acl->dacl_ace_count:%d", merge_acl_count, p_idfs_acl->dacl_ace_count);
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		rc = idfs_acl_printf(p_idfs_acl);
	}
out:
	if (allow_idfs_acl) {
		gsh_free(allow_idfs_acl);
		allow_idfs_acl = NULL;
	}
	if (deny_idfs_acl) {
		gsh_free(deny_idfs_acl);
		deny_idfs_acl = NULL;
	}
	*pp_idfs_acl = p_idfs_acl;
	return rc;

}

/* The function of the V4 ACL starts below*/

static const char *fsal_ace_type(fsal_acetype_t type)
{
	switch (type) {
	case FSAL_ACE_TYPE_ALLOW:
		return "A";
	case FSAL_ACE_TYPE_DENY:
		return "D ";
	case FSAL_ACE_TYPE_AUDIT:
		return "U";
	case FSAL_ACE_TYPE_ALARM:
		return "L";
	default:
		return "unknown";
	}
}

static const char *fsal_ace_perm(fsal_aceperm_t perm)
{
	static char buf[64];
	char *c = buf;

	if (perm & FSAL_ACE_PERM_READ_DATA)
		*c++ = 'r';
	if (perm & FSAL_ACE_PERM_WRITE_DATA)
		*c++ = 'w';
	if (perm & FSAL_ACE_PERM_APPEND_DATA)
		*c++ = 'a';
	if (perm & FSAL_ACE_PERM_EXECUTE)
		*c++ = 'x';
	if (perm & FSAL_ACE_PERM_DELETE)
		*c++ = 'd';
	if (perm & FSAL_ACE_PERM_DELETE_CHILD)
		*c++ = 'D';
	if (perm & FSAL_ACE_PERM_READ_ATTR)
		*c++ = 't';
	if (perm & FSAL_ACE_PERM_WRITE_ATTR)
		*c++ = 'T';
	if (perm & FSAL_ACE_PERM_READ_NAMED_ATTR)
		*c++ = 'n';
	if (perm & FSAL_ACE_PERM_WRITE_NAMED_ATTR)
		*c++ = 'N';
	if (perm & FSAL_ACE_PERM_READ_ACL)
		*c++ = 'c';
	if (perm & FSAL_ACE_PERM_WRITE_ACL)
		*c++ = 'C';
	if (perm & FSAL_ACE_PERM_WRITE_OWNER)
		*c++ = 'o';
	if (perm & FSAL_ACE_PERM_SYNCHRONIZE)
		*c++ = 'y';
	*c = '\0';

	return buf;
}

static const char *fsal_ace_flag(char *buf, fsal_aceflag_t flag)
{
	char *c = buf;

	if (flag & FSAL_ACE_FLAG_GROUP_ID)
		*c++ = 'g';
	if (flag & FSAL_ACE_FLAG_FILE_INHERIT)
		*c++ = 'f';
	if (flag & FSAL_ACE_FLAG_DIR_INHERIT)
		*c++ = 'd';
	if (flag & FSAL_ACE_FLAG_NO_PROPAGATE)
		*c++ = 'n';
	if (flag & FSAL_ACE_FLAG_INHERIT_ONLY)
		*c++ = 'i';
	if (flag & FSAL_ACE_FLAG_SUCCESSFUL)
		*c++ = 'S';
	if (flag & FSAL_ACE_FLAG_FAILED)
		*c++ = 'F';
	if (flag & FSAL_ACE_FLAG_INHERITED)
		*c++ = 'I';
	if (flag & FSAL_ACE_IFLAG_EXCLUDE_FILES)
		*c++ = 'x';
	if (flag & FSAL_ACE_IFLAG_EXCLUDE_DIRS)
		*c++ = 'X';
	if (flag & FSAL_ACE_IFLAG_SPECIAL_ID)
		*c++ = 'S';
	if (flag & FSAL_ACE_IFLAG_MODE_GEN)
		*c++ = 'G';
	*c = '\0';

	return buf;
}

fsal_acetype_t idfs_acl_type_2_ace_type(uint16_t type){

	switch (type) {
	case IDFS_ACE_TYPE_ACCESS_ALLOWED:
		return FSAL_ACE_TYPE_ALLOW;
	
	case IDFS_ACE_TYPE_ACCESS_DENIED:
		return FSAL_ACE_TYPE_DENY;
		
	case IDFS_ACE_TYPE_SYSTEM_AUDIT:
		return FSAL_ACE_TYPE_AUDIT;
		
	case IDFS_ACE_TYPE_SYSTEM_ALARM:
		return FSAL_ACE_TYPE_ALARM;
		
	default:
		return FSAL_ACE_TYPE_ALLOW;
	}
}

uint16_t ace_type_2_idfs_acl_type(fsal_acetype_t type){

	switch (type) {
	case FSAL_ACE_TYPE_ALLOW:
		return IDFS_ACE_TYPE_ACCESS_ALLOWED;
	
	case FSAL_ACE_TYPE_DENY:
		return IDFS_ACE_TYPE_ACCESS_DENIED;
		
	case FSAL_ACE_TYPE_AUDIT:
		return IDFS_ACE_TYPE_SYSTEM_AUDIT;
		
	case FSAL_ACE_TYPE_ALARM:
		return IDFS_ACE_TYPE_SYSTEM_ALARM;
		
	default:
		return IDFS_ACE_TYPE_ACCESS_ALLOWED;
	}
}

int ace_printf(fsal_acl_data_t acldata)
{

	int ret = 1;
	int k = 0;
	for (k = 0; k < acldata.naces; k++) {
		LogEvent(COMPONENT_FSAL, "ACE type:%#x, flag:%#x, iflag:(%#x), who:%u, perm:%#x",
					 acldata.aces[k].type,
					 acldata.aces[k].flag,
					 acldata.aces[k].iflag,
					 acldata.aces[k].who.uid,
					 acldata.aces[k].perm);
	};
	return ret;
}

int merge_idfs_acl_V4(idfs_acl_t *idfs_acl, idfs_acl_t **pp_idfs_acl){

	int size = 0;
	int i =0, j=0;
	int rc = 0 ;
	int before_merge_count = 0;
	int after_merge_count = 0;
	idfs_acl_entry_t *before_entry = NULL;
	idfs_acl_entry_t *after_entry = NULL;
	uint16_t tag = -1;
	/* apply memory for the merged acl*/
	before_merge_count = idfs_acl->dacl_ace_count;	
	idfs_acl_t *p_idfs_acl = NULL;
	size = sizeof(idfs_acl_t) + (before_merge_count) * sizeof(idfs_acl_entry_t);
	p_idfs_acl = (idfs_acl_t *)malloc(size);
	if (!p_idfs_acl){
		LogEvent(COMPONENT_IDFS_ACL, "malloc failed for p_idfs_acl");
		rc = -1;
		goto out;
	}
	memset(p_idfs_acl, 0, size);

	/* initializing the acl */
	p_idfs_acl->acl_ver = idfs_acl->acl_ver;
	p_idfs_acl->acl_type = idfs_acl->acl_type;
	p_idfs_acl->sd_ver =  idfs_acl->sd_ver;
	p_idfs_acl->ntacl_ver =  idfs_acl->ntacl_ver;
	p_idfs_acl->ctrl_flags =  idfs_acl->ctrl_flags;
	p_idfs_acl->owner = idfs_acl->owner;
	p_idfs_acl->group = idfs_acl->group;



	for (i = 0; i < before_merge_count; i++) {
		before_entry = &idfs_acl->entry[i];
		
		/* traversing the acl, fill in merged acl */
		for (j = 0; j < before_merge_count; j++) {
			after_entry = &p_idfs_acl->entry[j];
			/*  if the acl is same, merge, num stays the same*/
			if (CHECK_IF_IDFS_ACL_ENTRIES_SAME_V4(before_entry, after_entry)) {
				after_entry->perm |= before_entry->perm;
				break;
			}
			/*if the acl is not same, find the empty acl, num++*/
			if(IDFS_ACL_ENTRY_SHOULD_SKIP_V4(after_entry)){
				copy_entry1_2_entry2(after_entry, before_entry, before_entry->perm);	
				after_merge_count++;
				break;
			}
		}
	}
	/*record the num after merged*/
	p_idfs_acl->dacl_ace_count = after_merge_count;
	/*print the acl */
	rc = idfs_acl_printf(p_idfs_acl);

	LogDebug(COMPONENT_IDFS_ACL, "before_merge_count:%d, after_merge_count:%d,idfs_acl->dacl_ace_count:%d, p_idfs_acl->dacl_ace_count:%d", 
		before_merge_count, after_merge_count, idfs_acl->dacl_ace_count, p_idfs_acl->dacl_ace_count);
	/*reapply memory, the newly memory must be smaller than the original memory*/
	size = sizeof(idfs_acl_t) + (after_merge_count) * sizeof(idfs_acl_entry_t);
	p_idfs_acl = (idfs_acl_t *)gsh_realloc(p_idfs_acl, size);
	p_idfs_acl->dacl_ace_count = after_merge_count;
	/*---------determine if the mask should be completed--------*/
	bool add_access_mask = TRUE;
	bool add_default_mask = FALSE;
	uint32_t access_mask = 0;
	uint32_t default_mask = 0;
	int index_access_mask = 0;
	int index_default_mask = 0;

	before_merge_count = p_idfs_acl->dacl_ace_count;
	
	/* find mask  */
	for (i = 0; i < before_merge_count; i++) {
		before_entry = &p_idfs_acl->entry[i];
		
		/*access acl, find IDFS_ACL_MASK*/
		if (ACL_ECTRY_IS_ACCESS(before_entry->flags)) {
			LogDebug(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_ACCESS");
			if(before_entry->tag == IDFS_ACL_MASK){ 
				LogDebug(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_ACCESS, add_access_mask = false");
				add_access_mask = false;
				index_access_mask = i;
				continue;
			}
			/* get the value of access mask */			
			tag = before_entry->tag;
			switch (tag) {
				case IDFS_ACL_GROUP_OBJ:
					access_mask |= before_entry->perm;
					break;
				
				case IDFS_ACL_USER:
					access_mask |= before_entry->perm;
					break;
				
				case IDFS_ACL_GROUP:
					access_mask |= before_entry->perm;
					break;

				case IDFS_ACL_USER_OBJ:
				case IDFS_ACL_MASK:
				case IDFS_ACL_OTHER:
					break;
				
				default:
					break;
			}
		}
		/*inherit acl, find IDFS_ACL_MASK*/
		if (ACL_ECTRY_IS_FILE_INHERIT(before_entry->flags) || ACL_ECTRY_IS_SUBDIR_INHERIT(before_entry->flags)){
			LogDebug(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_FILE_INHERIT");
			add_default_mask = true;
			if(before_entry->tag == IDFS_ACL_MASK){ 
				add_default_mask = false;
				index_default_mask = i;
				LogDebug(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_FILE_INHERIT, add_access_mask = false");
				continue;
			}
			/* get the value of inherit mask */
			tag = before_entry->tag;
			switch (tag) {
				case IDFS_ACL_GROUP_OBJ:
					default_mask |= before_entry->perm;
					break;
				
				case IDFS_ACL_USER:
					default_mask |= before_entry->perm;
					break;
				
				case IDFS_ACL_GROUP:
					default_mask |= before_entry->perm;
					break;
				
				case IDFS_ACL_USER_OBJ:
				case IDFS_ACL_MASK:
				case IDFS_ACL_OTHER:
					break;
				
				default:
					break;
					
			}

		}

	}

	/* add mask  */
	int add_access_mask_num = 0;

	if(add_access_mask){
		add_access_mask_num =  p_idfs_acl->dacl_ace_count;
		LogDebug(COMPONENT_IDFS_ACL, "add_access_mask_num:%d, p_idfs_acl->dacl_ace_count:%d",
			add_access_mask_num, p_idfs_acl->dacl_ace_count);

		size = sizeof(idfs_acl_t) + (add_access_mask_num + 1)* sizeof(idfs_acl_entry_t);
		p_idfs_acl = (idfs_acl_t *)gsh_realloc(p_idfs_acl, size);
		p_idfs_acl->dacl_ace_count += 1;
		LogDebug(COMPONENT_IDFS_ACL, "add_access_mask_num:%d, p_idfs_acl->dacl_ace_count:%d",
			add_access_mask_num, p_idfs_acl->dacl_ace_count);

		p_idfs_acl->entry[add_access_mask_num ].type = IDFS_ACE_TYPE_ACCESS_ALLOWED;
		p_idfs_acl->entry[add_access_mask_num].flags = 0;
		p_idfs_acl->entry[add_access_mask_num].tag = IDFS_ACL_MASK;
		p_idfs_acl->entry[add_access_mask_num].reserved = 0;
		p_idfs_acl->entry[add_access_mask_num].uid = -1;
		p_idfs_acl->entry[add_access_mask_num].gid = -1;
		p_idfs_acl->entry[add_access_mask_num].perm = access_mask;
	
	}else{
		LogDebug(COMPONENT_IDFS_ACL, "refresh access_mask");
		if (p_idfs_acl->entry[index_access_mask].tag == IDFS_ACL_MASK){
			p_idfs_acl->entry[index_access_mask].perm = access_mask;
		}else{
			LogEvent(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_ACCESS, index_access_mask:%d is error,can not find tag:IDFS_ACL_MASK.", index_default_mask);

		}

	}

	int add_default_mask_num = 0;
	if(add_default_mask){
		add_default_mask_num =	p_idfs_acl->dacl_ace_count;
		size = sizeof(idfs_acl_t) + (add_default_mask_num  + 1)* sizeof(idfs_acl_entry_t);
		p_idfs_acl = (idfs_acl_t *)gsh_realloc(p_idfs_acl, size);
		p_idfs_acl->dacl_ace_count += 1;
		
		LogDebug(COMPONENT_IDFS_ACL, "add_default_mask_num:%d, p_idfs_acl->dacl_ace_count:%d",
			add_default_mask_num, p_idfs_acl->dacl_ace_count);

		p_idfs_acl->entry[add_default_mask_num].type = IDFS_ACE_TYPE_ACCESS_ALLOWED;
		p_idfs_acl->entry[add_default_mask_num].flags = IDFS_ACE_SUBDIR_INHERIT | IDFS_ACE_FILE_INHERIT | IDFS_ACE_INHERIT_ONLY;
		p_idfs_acl->entry[add_default_mask_num].tag = IDFS_ACL_MASK;
		p_idfs_acl->entry[add_default_mask_num].reserved = 0;
		p_idfs_acl->entry[add_default_mask_num].uid = -1;
		p_idfs_acl->entry[add_default_mask_num].gid = -1;
		p_idfs_acl->entry[add_default_mask_num].perm = default_mask;
	
	}else if(add_default_mask_num != 0){
		LogDebug(COMPONENT_IDFS_ACL, "refresh default_mask");
		if (p_idfs_acl->entry[index_default_mask].tag == IDFS_ACL_MASK){
			p_idfs_acl->entry[index_default_mask].perm = default_mask;
		}else{
			LogEvent(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_DEFAULT, index_default_mask:%d is error,can not find tag:IDFS_ACL_MASK.", index_access_mask);

		}
	}

	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		rc = idfs_acl_printf(p_idfs_acl);
	}


	LogDebug(COMPONENT_IDFS_ACL, "add_default_mask_num:%d, p_idfs_acl->dacl_ace_count:%d",
		add_default_mask_num, p_idfs_acl->dacl_ace_count);

	rc = p_idfs_acl->dacl_ace_count;
	*pp_idfs_acl = p_idfs_acl;

out:
	return rc;
}

int idfs_acl_2_ace(idfs_acl_t *idfs_acl, fsal_ace_t **ace){

	int end = 0;
	idfs_acl_entry_t ea_entry;
	uint16_t tag = -1;

	fsal_ace_t *pace = NULL;
	pace = *ace;
	int count = 0;
	count = idfs_acl->dacl_ace_count;
	uint32_t access_mask = 0;
	uint32_t default_mask = 0;

	LogDebug(COMPONENT_IDFS_ACL, "idfs_acl_2_ace, ace_count:%d", idfs_acl->dacl_ace_count);
	
	/* find the mask for access and inherit*/
	for (end = 0; end < idfs_acl->dacl_ace_count; end++) {
		ea_entry = idfs_acl->entry[end];
		if (ACL_ECTRY_IS_ACCESS(ea_entry.flags)) {
			if(ea_entry.tag == IDFS_ACL_MASK){ 
				LogDebug(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_ACCESS, access_mask = %#x", ea_entry.perm);
				access_mask = ea_entry.perm;
				break;
			}
		}

		if (ACL_ECTRY_IS_FILE_INHERIT(ea_entry.flags) || ACL_ECTRY_IS_SUBDIR_INHERIT(ea_entry.flags)){
			if(ea_entry.tag == IDFS_ACL_MASK){ 
				default_mask = ea_entry.perm;
				LogDebug(COMPONENT_IDFS_ACL, "ACL_ECTRY_IS_FILE_INHERIT, default_mask = %#x", ea_entry.perm);
				break;
			}
		}
	
	}
	/*acl convert to ace*/
	for (end = 0; end < idfs_acl->dacl_ace_count; end++) {
		
		ea_entry = idfs_acl->entry[end];
		tag = ea_entry.tag;
		/* skip mask,num--,v4 ace have not mask*/
		if(tag == IDFS_ACL_MASK){
			LogDebug(COMPONENT_IDFS_ACL, "find mask , count:%d", count);
			count --;
			continue;
		}
		
		pace->iflag = 0;
		pace->flag = ea_entry.flags;
		switch (tag) {
		case IDFS_ACL_USER_OBJ:
			pace->iflag |= FSAL_ACE_IFLAG_SPECIAL_ID;
			pace->who.uid = FSAL_ACE_SPECIAL_OWNER;
			pace->perm = ea_entry.perm;
			
			break;

		case IDFS_ACL_GROUP_OBJ:
			pace->iflag |= FSAL_ACE_IFLAG_SPECIAL_ID;
			pace->who.uid = FSAL_ACE_SPECIAL_GROUP;
			pace->perm = ea_entry.perm;
			if (ACL_ECTRY_IS_ACCESS(ea_entry.flags)) {
				LogDebug(COMPONENT_IDFS_ACL, "find mask , count:%d", count);
				if(access_mask != 0)
					pace->perm = pace->perm & access_mask;
			}
			if (ACL_ECTRY_IS_FILE_INHERIT(ea_entry.flags) || ACL_ECTRY_IS_SUBDIR_INHERIT(ea_entry.flags)){
				if(default_mask != 0)
					pace->perm = pace->perm & default_mask;
			}
			break;
			
		case IDFS_ACL_MASK:
			pace->iflag |= FSAL_ACE_IFLAG_SPECIAL_ID;
			pace->who.uid = FSAL_ACE_SPECIAL_MASK;
			pace->perm = ea_entry.perm;
			
			break;
			
		case IDFS_ACL_OTHER:
			pace->iflag |= FSAL_ACE_IFLAG_SPECIAL_ID;
			pace->who.uid = FSAL_ACE_SPECIAL_EVERYONE;
			pace->perm = ea_entry.perm;
			
			break;

		case IDFS_ACL_USER:
			pace->who.uid = ea_entry.uid;
			pace->perm = ea_entry.perm;
			if (ACL_ECTRY_IS_ACCESS(ea_entry.flags)) {
				if(access_mask != 0)
					pace->perm = pace->perm & access_mask;
			}
			if (ACL_ECTRY_IS_FILE_INHERIT(ea_entry.flags) || ACL_ECTRY_IS_SUBDIR_INHERIT(ea_entry.flags)){
				if(default_mask != 0)
					pace->perm = pace->perm & default_mask;
			}

			break;

		case IDFS_ACL_GROUP:
			pace->flag |= FSAL_ACE_FLAG_GROUP_ID;
			pace->who.uid = ea_entry.gid;
			pace->perm = ea_entry.perm;
			if (ACL_ECTRY_IS_ACCESS(ea_entry.flags)) {
				if(access_mask != 0)
					pace->perm = pace->perm & access_mask;
			}
			if (ACL_ECTRY_IS_FILE_INHERIT(ea_entry.flags) || ACL_ECTRY_IS_SUBDIR_INHERIT(ea_entry.flags)){
				if(default_mask != 0)
					pace->perm = pace->perm & default_mask;
			}

			break;

		default:
			goto out;
		}
		
		pace->type = idfs_acl_type_2_ace_type(ea_entry.type);
		pace += 1;
	}	
	LogDebug(COMPONENT_IDFS_ACL, "idfs_acl_2_ace: %d", count);

out:
	return count;
}


#ifdef IDFSFS_POSIX_ACL

int idfs_get_posix_acl2(struct idfs_export *export,
	struct idfs_handle *objhandle, bool is_dir, acl_type_t type, acl_t *p_acl, struct fsal_attrlist *attrs)
{
	int rc = 0, size = 0;
	fsal_status_t fsal_status_uid = { ERR_FSAL_NO_ERROR, 0 };
	acl_t acl_tmp = NULL;
	idfs_acl_t *idfs_acl = NULL;
	idfs_acl_t *idfs_acl_getx = NULL;
	idfs_acl_t *after_merge_idfs_acl = NULL;
	idfs_acl_t *dst_acl = NULL;
	gid_t* gids = NULL;
	/* Get extended attribute size */
	size = fsal_idfs_ll_getxattr(export->cmount, objhandle->i, SYSTEM_IDFS_keyname,
				NULL, 0, &op_ctx->creds);
	LogFullDebug(COMPONENT_IDFS_ACL, "idfs_get_posix_acl2-getxattr returned size %d ,in :%lu, fileid:%lu", size, (uint64_t)objhandle->i, objhandle->handle.fileid);
	if (size <= 0 ) {
		if (((size == -ENODATA) || (size == 0)) && (type == ACL_TYPE_ACCESS)) {
				rc = idfs_acl_from_mode(&idfs_acl, type ,is_dir, attrs);
				acl_tmp = idfs_acl_2_posix_acl(idfs_acl,is_dir, type);	
				if(!acl_tmp){
					LogMajor(COMPONENT_IDFS_ACL, "idfs_get_posix_acl2 ,ACL_TYPE_ACCESS is null");
					rc = 0;
					goto out;
				}
				rc = 0;
				goto out;
		}
		if ( ((size == -ENODATA) || (size == 0)) && (type == ACL_TYPE_DEFAULT)) {
			rc = 0;
			goto out;
		}
		LogMajor(COMPONENT_IDFS_ACL, "failed to fsal_idfs_ll_getxattr: %d", size);
		rc = -1;
		goto out;
	}
	idfs_acl_getx = (idfs_acl_t *)malloc(size);
	if (!idfs_acl_getx){
		LogMajor(COMPONENT_IDFS_ACL,"malloc failed for idfs_acl");
		rc = -1;
		goto out;

	}
	memset(idfs_acl_getx, 0, size);


	/* Read extended attribute's value */
	rc = fsal_idfs_ll_getxattr(export->cmount, objhandle->i, SYSTEM_IDFS_keyname,
				(char *)idfs_acl_getx, size, &op_ctx->creds);
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "getxattr -value  returned %d", rc);
		goto out;
	}	
	
	rc = ckeck_idfs_acl(idfs_acl_getx, size);
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "ckeck_idfs_acl, idfs_acl size is error , returned %d", rc);
		goto out;
	}
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		LogDebug(COMPONENT_IDFS_ACL, "ckeck_idfs_acl is OVER, returned %d", rc);
		rc = idfs_acl_printf(idfs_acl_getx);
	}
	LogDebug(COMPONENT_IDFS_ACL, "idfs_acl_getx->acl_type is %d", idfs_acl_getx->acl_type);
	attrs->idfs_acl_type = idfs_acl_getx->acl_type;
	if(idfs_acl_getx->acl_type == IDFS_ACL_TYPE_NTFS){
		fsal_status_t fsal_status_map = { ERR_FSAL_NO_ERROR, 0 };
		unsigned int ntfs_uid = 0;
		int rc =0;
		fsal_status_t fsal_status_ntfs_to_nfs = { ERR_FSAL_NO_ERROR, 0};
		fsal_status_map = objhandle->handle.obj_ops->trans_usermap(&objhandle->handle, op_ctx->creds.caller_uid, &ntfs_uid);
		LogDebug(COMPONENT_IDFS_ACL, "trans_usermap return :%d, creds.caller_uid:%u, ntfs_uid:%u, fileid:%lu", fsal_status_map.major, op_ctx->creds.caller_uid, ntfs_uid, (uint64_t)objhandle->handle.fileid);
		idfs_user_info_t user_info;
		user_info.src_uid = op_ctx->creds.caller_uid;
		user_info.src_gid = op_ctx->creds.caller_gid;
		user_info.owner_uid = idfs_acl_getx->owner;
		user_info.owner_gid = idfs_acl_getx->group;
		if (!FSAL_IS_ERROR(fsal_status_map)){
			int gids_count = 0;
			fsal_status_uid = objhandle->handle.obj_ops->uid_2_gids(&objhandle->handle, op_ctx->ctx_export->tenant, ntfs_uid, &gids, &gids_count);
			user_info.muid = ntfs_uid;
			user_info.mgids = gids;
			user_info.mgids_num = gids_count;

			if(!FSAL_IS_ERROR(fsal_status_uid)){
				fsal_status_ntfs_to_nfs = objhandle->handle.obj_ops->ntfs_acl_to_unix_acl(&objhandle->handle, idfs_acl_getx, &user_info, &dst_acl);
				LogDebug(COMPONENT_IDFS_ACL, "ntfs_acl_to_unix_acl return :%d, get_groups_from_uid return :%d", fsal_status_ntfs_to_nfs.major, rc);
			}else{
				LogWarn(COMPONENT_IDFS_ACL, "get_groups_from_uid error, return :%d", rc);
				goto out;	
			}
	
		}else{
			user_info.muid = -1;
			user_info.mgids = NULL;
			user_info.mgids_num = 0;
			fsal_status_ntfs_to_nfs = objhandle->handle.obj_ops->ntfs_acl_to_unix_acl(&objhandle->handle, idfs_acl_getx, &user_info, &dst_acl);
			LogDebug(COMPONENT_IDFS_ACL, "ntfs_acl_to_unix_acl return :%d", fsal_status_ntfs_to_nfs.major);

		}
		rc = idfs_acl_printf(dst_acl);
		if(rc < 0){
			LogDebug(COMPONENT_IDFS_ACL, "dst_acl is null, idfs_acl_printf return:%d", rc);
			goto out;
		}
		size_t dst_size = 0;
		dst_size = IDFS_ACL_SIZE(dst_acl->dacl_ace_count);
		LogDebug(COMPONENT_IDFS_ACL, "dst_size:%lu", dst_size);
		idfs_acl_getx = (idfs_acl_t *) gsh_realloc(idfs_acl_getx, dst_size);
		memset(idfs_acl_getx, 0, dst_size);
		memcpy(idfs_acl_getx, dst_acl, dst_size);
	}
	/* access : merge allow and deny */
	
	rc = merge_allow_deny_acl(idfs_acl_getx, type, &after_merge_idfs_acl);
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "merge_allow_deny_acl is error , returned %d", rc);
		goto out;
	}
	
	/* Convert extended attribute to posix acl */
	acl_tmp = idfs_acl_2_posix_acl(after_merge_idfs_acl, is_dir, type);
	
	if (!acl_tmp) {
		LogDebug(COMPONENT_IDFS_ACL,
				"failed to convert xattr to posix acl");
		//rc = -EFAULT;
		goto out;
	}

out:
	*p_acl = acl_tmp;
	if(after_merge_idfs_acl){
		gsh_free(after_merge_idfs_acl);
		after_merge_idfs_acl = NULL;       
	}
        if(idfs_acl){
                gsh_free(idfs_acl);
                idfs_acl = NULL;
        }
	if(idfs_acl_getx){
		gsh_free(idfs_acl_getx);
		idfs_acl_getx = NULL;
	}
	if(dst_acl){
		gsh_free(dst_acl);
		dst_acl = NULL;
	}
	if(gids){
		fsal_status_uid = objhandle->handle.obj_ops->usermgr_result_free_gids(&objhandle->handle, gids);
	}
	return rc;
}
fsal_status_t idfs_set_acl_v3(struct idfs_export *export,
	struct idfs_handle *objhandle, bool is_dir, struct fsal_attrlist *attrs, bool is_default)
{
	int size = 0, count = 0, rc = 0;
	acl_t acl = NULL;
	acl_type_t type;
	
	void *value = NULL;
	fsal_status_t status = {0, 0};
  
	acl_t e_acl = NULL, i_acl = NULL;
	int e_count = 0, i_count = 0;
	idfs_acl_t *idfs_acl = NULL;
	if (!attrs->acl) {
		LogWarn(COMPONENT_IDFS_ACL, "acl is empty");
		status = fsalstat(ERR_FSAL_FAULT, 0);
		goto out;
	}

	if (is_default) {
		type = ACL_TYPE_DEFAULT;
	} else {
		type = ACL_TYPE_ACCESS;
	}

	LogDebug(COMPONENT_IDFS_ACL, "idfs_set_acl_v3, type:%#x, fileid:%lu, inode:%lu", type, objhandle->handle.fileid, (uint64_t)objhandle->i);

	acl = fsal_acl_2_posix_acl(attrs->acl, type);

	if (acl_valid(acl) != 0 && acl!= NULL ) {
		LogWarn(COMPONENT_FSAL, "failed to convert fsal acl to posix acl");
		status = fsalstat(ERR_FSAL_FAULT, 0);
		goto out;
	}
	count = acl_entries(acl);

	if(!is_default){
		rc = idfs_get_posix_acl2(export, objhandle, is_dir, ACL_TYPE_DEFAULT,  &i_acl, attrs);
		if (rc < 0) {
			LogMajor(COMPONENT_IDFS_ACL,
					"failed to get posix acl: %#x, rc:%d, count:%d", type, rc, count);
			status = fsalstat(ERR_FSAL_FAULT, 0);
			goto out;
		}
		
		i_count = ace_count(i_acl);
		if (i_count >= 0) {
			rc = posix_acl_2_idfs_acl(acl, i_acl, &idfs_acl, size, is_dir, ACL_TYPE_ACCESS, attrs);
			if (rc < 0) {
				LogMajor(COMPONENT_IDFS_ACL, "failed to posix_acl_2_idfs_acl: %d", rc);
				status = fsalstat(ERR_FSAL_FAULT, 0);
				goto out;
			}
		}
	}else if (is_default) {
		rc = idfs_get_posix_acl2(export, objhandle, is_dir, ACL_TYPE_ACCESS, &e_acl, attrs);
		if (rc < 0) {
			LogMajor(COMPONENT_IDFS_ACL, "failed to get posix acl, rc:%d, type: %#x", rc, type);
			status = fsalstat(ERR_FSAL_FAULT, 0);
			goto out;
		}
		e_count = ace_count(e_acl);
		
		if (e_count >= 0) {
			rc = posix_acl_2_idfs_acl(e_acl, acl, &idfs_acl, size, is_dir, ACL_TYPE_DEFAULT, attrs);
			if (rc < 0) {
				LogMajor(COMPONENT_IDFS_ACL, "failed to posix_acl_2_idfs_acl: %d", rc);
				status = fsalstat(ERR_FSAL_FAULT, 0);
				goto out;
			}
		}
	}
	
	if (idfs_acl != NULL){
		if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
			LogDebug(COMPONENT_IDFS_ACL, "idfs_set_acl_v3, before fsal_idfs_ll_setxattr, printf idfs_acl, type:%#x", type);
			rc = idfs_acl_printf(idfs_acl);
		}
		size = sizeof(idfs_acl_t) + idfs_acl->dacl_ace_count * sizeof(idfs_acl_entry_t);
		rc = fsal_idfs_ll_setxattr(export->cmount, objhandle->i,
					SYSTEM_IDFS_keyname, (char *)idfs_acl, size, 0, &op_ctx->creds);
		if (rc < 0) {
			status = idfs2fsal_error(rc);
			goto out;
		}
	}
	
	if ((type == ACL_TYPE_ACCESS) && (idfs_acl != NULL)){
		/* refresh mode*/ 
		attrs->mode = idfs_acl_2_mode(idfs_acl, is_dir);
		uint32_t mask = 0;
		struct idfs_statx stx;
		memset(&stx, 0, sizeof(stx));
		mask |= IDFS_SETATTR_MODE;
		stx.stx_mode = fsal2unix_mode(attrs->mode);
		rc = fsal_idfs_ll_setattr(export->cmount, objhandle->i, &stx, mask,
						&op_ctx->creds);
	}
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "acl refresh mode, failed to fsal_idfs_ll_setattr: %d", rc);
		status = idfs2fsal_error(rc);
		goto out;
	}

out:
	if (acl) {
		acl_free((void *)acl);
		acl = NULL;
	}

	if (e_acl) {
		acl_free((void *)e_acl);
		e_acl = NULL;
	}

	if (i_acl) {
		acl_free((void *)i_acl);
		i_acl = NULL;
	}

	if (idfs_acl) {
		gsh_free(idfs_acl);
		idfs_acl = NULL;
	}

	if (value) {
		gsh_free(value);
		value = NULL;
	}

	return status;
}

int idfs_get_acl_v3(struct idfs_export *export, struct idfs_handle *objhandle,
	bool is_dir, struct fsal_attrlist *attrs)
{
	acl_t e_acl = NULL, i_acl = NULL;
	fsal_acl_data_t acldata;
	fsal_ace_t *pace = NULL;
	fsal_acl_status_t aclstatus;
	int e_count = 0, i_count = 0, new_count = 0, new_i_count = 0;
	int rc = 0;
	acl_type_t type;
	type = ACL_TYPE_ACCESS;
	LogDebug(COMPONENT_IDFS_ACL, "idfs_get_acl_v3, type:%#x, fileid:%lu, inode:%lu", type, objhandle->handle.fileid, (uint64_t)objhandle->i);
	rc = idfs_get_posix_acl2(export, objhandle, is_dir, type, &e_acl, attrs);

	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "failed to get posix acl:ACL_EA_ACCESS  %s", ACL_EA_ACCESS);
		goto out;
	}
	e_count = ace_count(e_acl);

	if (is_dir) {
		type = ACL_TYPE_DEFAULT;
		rc = idfs_get_posix_acl2(export, objhandle, is_dir, type, &i_acl, attrs);

		if (rc < 0) {
			LogMajor(COMPONENT_IDFS_ACL, "failed to get posix acl:ACL_EA_DEFAULT %s", ACL_EA_DEFAULT);
		} else {
			i_count = ace_count(i_acl);
		}
	}
	acldata.naces = 2 * (e_count + i_count);
	if (!acldata.naces) {
		rc = 0;
		goto out;
	}

	acldata.aces = (fsal_ace_t *) nfs4_ace_alloc(acldata.naces);
	pace = acldata.aces;

	if (e_count > 0) {
		new_count = posix_acl_2_fsal_acl(e_acl, is_dir, false, &pace);
	} else {
		LogDebug(COMPONENT_FSAL,
			"effective acl is not set for this object");
	}

	if (i_count > 0) {
		new_i_count = posix_acl_2_fsal_acl(i_acl, true, true, &pace);
		new_count += new_i_count;
	} else {
		LogDebug(COMPONENT_FSAL,
			"Inherit acl is not set for this directory");
	}
	
	/* Reallocating acldata into the required size */
	acldata.aces = (fsal_ace_t *) gsh_realloc(acldata.aces,
					new_count*sizeof(fsal_ace_t));
	acldata.naces = new_count;
	attrs->acl = nfs4_acl_new_entry(&acldata, &aclstatus);
	if (attrs->acl == NULL) {
		LogCrit(COMPONENT_IDFS_ACL, "failed to create a new acl entry");
		rc = -EFAULT;
		goto out;
	}

	rc = 0;
	attrs->valid_mask |= ATTR_ACL;

out:
	if (e_acl) {
		acl_free((void *)e_acl);
		e_acl = NULL;
	}

	if (i_acl) {
		acl_free((void *)i_acl);
		i_acl = NULL;
	}

	return rc;
}

fsal_status_t idfs_set_acl_v4(struct idfs_export *export,
	struct idfs_handle *objhandle, bool is_dir, struct fsal_attrlist *attrs, bool is_default)
{

	acl_type_t type;
	fsal_status_t status = {0, 0};
	int real_size = 0;
	idfs_acl_t *ace_idfs_acl = NULL;
	idfs_acl_t *after_merge_idfs_acl = NULL;
	fsal_ace_t *f_ace = NULL;
	int rc = 0, size = 0;

	if (!attrs->acl) {
		LogWarn(COMPONENT_IDFS_ACL, "acl is empty");
		status = fsalstat(ERR_FSAL_FAULT, 0);
		goto out;
	}
	if (is_default) {
		type = ACL_TYPE_DEFAULT;
	} else {
		type = ACL_TYPE_ACCESS;
	}
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		char fbuf[16];
		char ibuf[16];
		for (f_ace = attrs->acl->aces;
			f_ace < attrs->acl->aces + attrs->acl->naces; f_ace++) {

			LogDebug(COMPONENT_IDFS_ACL, "ACE %s:%s(%s):%u:%s",
					 fsal_ace_type(f_ace->type),
					 fsal_ace_flag(fbuf, f_ace->flag),
					 fsal_ace_flag(ibuf, f_ace->iflag),
					 f_ace->who.uid,
					 fsal_ace_perm(f_ace->perm));
		};
	}

	LogDebug(COMPONENT_IDFS_ACL, "idfs_set_acl_v4, type:%#x, fileid:%lu, inode:%lu", type, objhandle->handle.fileid, (uint64_t)objhandle->i);

	real_size = sizeof(idfs_acl_t) + attrs->acl->naces * sizeof(idfs_acl_entry_t);
	ace_idfs_acl = (idfs_acl_t *)malloc(real_size);
	if (!ace_idfs_acl){
		LogMajor(COMPONENT_IDFS_ACL,"malloc failed for idfs_acl");
		status = fsalstat(ERR_FSAL_FAULT, 0);
		goto out;
	}
	memset(ace_idfs_acl, 0, real_size);

	ace_idfs_acl->acl_ver = IDFS_ACL_VERSION_V11;
	ace_idfs_acl->acl_type = IDFS_ACL_TYPE_NFSV4;
	ace_idfs_acl->sd_ver = 0;
	ace_idfs_acl->ntacl_ver = 0;
	ace_idfs_acl->ctrl_flags = 0;
	ace_idfs_acl->owner = attrs->owner;
	ace_idfs_acl->group = attrs->group;

	/* id  tag perm */
	uint16_t tag = -1;
	unsigned int id = 0;
	int entry_id = 0;
	for (f_ace = attrs->acl->aces;
		f_ace < attrs->acl->aces + attrs->acl->naces; f_ace++) {
		
		if (IS_FSAL_ACE_SPECIAL_ID(*f_ace)) {
			if (IS_FSAL_ACE_SPECIAL_OWNER(*f_ace)){
				tag = IDFS_ACL_USER_OBJ;
				id = attrs->owner;
			}
			if (IS_FSAL_ACE_SPECIAL_GROUP(*f_ace)){
				tag = IDFS_ACL_GROUP_OBJ;
				id = attrs->group;
			}
			if (IS_FSAL_ACE_SPECIAL_MASK(*f_ace)){
				tag = IDFS_ACL_MASK;
				id = -1;
			}
			
			if (IS_FSAL_ACE_SPECIAL_EVERYONE(*f_ace)){
				tag = IDFS_ACL_OTHER;
				id = -1;
			}

		} else {
			id = GET_FSAL_ACE_WHO(*f_ace);
			if (IS_FSAL_ACE_GROUP_ID(*f_ace)){
				tag = IDFS_ACL_GROUP;
			}
			else{
				tag = IDFS_ACL_USER;
			}
		}

		ace_idfs_acl->entry[entry_id].uid = id;
		ace_idfs_acl->entry[entry_id].tag = tag;
		
		ace_idfs_acl->entry[entry_id].type = ace_type_2_idfs_acl_type(f_ace->type);
		ace_idfs_acl->entry[entry_id].perm = f_ace->perm;
		ace_idfs_acl->entry[entry_id].flags = f_ace->flag;
		
		entry_id +=1;

	}
	ace_idfs_acl->dacl_ace_count = entry_id ;
	/*print the acl*/
	rc = idfs_acl_printf(ace_idfs_acl);
	if (rc < 0) {
		status = idfs2fsal_error(rc);
		LogMajor(COMPONENT_IDFS_ACL,"idfs_acl_printf is error, rc:%d, status:%d", rc, status.major);
		goto out;
	}
	
	/* Ace to Acl is over, ACL has duplication and needs to be merged. The mask need to be completed*/
	rc = merge_idfs_acl_V4(ace_idfs_acl, &after_merge_idfs_acl);
	if (rc < 0) {
		status = idfs2fsal_error(rc);
		LogMajor(COMPONENT_IDFS_ACL,"merge idfs acl for v4 is error, rc:%d, status:%d", rc, status.major);
		goto out;
	}

	/*print the acl*/
	rc = idfs_acl_printf(after_merge_idfs_acl);
	if (rc < 0) {
		status = idfs2fsal_error(rc);
		LogMajor(COMPONENT_IDFS_ACL,"idfs_acl_printf is error, rc:%d, status:%d", rc, status.major);
		goto out;
	}
	/*final set the acl*/
	if (after_merge_idfs_acl != NULL){
		size = sizeof(idfs_acl_t) + after_merge_idfs_acl->dacl_ace_count * sizeof(idfs_acl_entry_t);
		rc = fsal_idfs_ll_setxattr(export->cmount, objhandle->i,
					SYSTEM_IDFS_keyname, (char *)after_merge_idfs_acl, size, 0, &op_ctx->creds);
		if (rc < 0) {
			status = idfs2fsal_error(rc);
			LogMajor(COMPONENT_IDFS_ACL,"fsal_idfs_ll_setxattr is error, rc:%d, status:%d, fileid:%lu", rc, status.major, objhandle->handle.fileid);
			goto out;
		}
	}

out:

	if (ace_idfs_acl) {
		gsh_free(ace_idfs_acl);
		ace_idfs_acl = NULL;
	}

	if (after_merge_idfs_acl) {
		gsh_free(after_merge_idfs_acl);
		after_merge_idfs_acl = NULL;
	}

	return status;
}

int idfs_get_acl_v4(struct idfs_export *export, struct idfs_handle *objhandle,
	bool is_dir, struct fsal_attrlist *attrs)
{
	fsal_acl_data_t acldata;
	fsal_acl_status_t aclstatus;
	int new_count = 0;
	int rc = 0;
	idfs_acl_t *idfs_acl = NULL;
	int size = 0;

	/* Get extended attribute size */
	size = fsal_idfs_ll_getxattr(export->cmount, objhandle->i, SYSTEM_IDFS_keyname,
				NULL, 0, &op_ctx->creds);
	
	LogDebug(COMPONENT_IDFS_ACL, "idfs_get_acl_v4, getxattr returned size %d, in :%lu, fileid:%lu",
		size, (uint64_t)objhandle->i, objhandle->handle.fileid);

	if (size <= 0 ) {
		if ((size == -ENODATA) || (size == 0)) {
			size = idfs_acl_from_mode(&idfs_acl, ACL_TYPE_ACCESS, is_dir, attrs);
			LogDebug(COMPONENT_IDFS_ACL,"idfs_acl_from_mode, goto idfs_acl_transform_ace;");
			goto transform;
		}
		LogMajor(COMPONENT_IDFS_ACL, "failed to fsal_idfs_ll_getxattr: %d", size);
		rc = size;
		goto out;
	}

	idfs_acl = (idfs_acl_t *)malloc(size);
	if (!idfs_acl){
		LogMajor(COMPONENT_IDFS_ACL,"malloc failed for idfs_acl");
		rc = -1;
		goto out;

	}
	memset(idfs_acl, 0, size);


	/* Read extended attribute's value */
	rc = fsal_idfs_ll_getxattr(export->cmount, objhandle->i, SYSTEM_IDFS_keyname,
				(char *)idfs_acl, size, &op_ctx->creds);
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "fsal_idfs_ll_getxattr get value error, returned %d", rc);
		rc = -1;
		goto out;
	}
	
transform:	
	rc = ckeck_idfs_acl(idfs_acl, size);
	
	if (rc < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "ckeck_idfs_acl, idfs_acl size is error , returned %d", rc);
		/*rc = -1;*/
		goto out;
	}
	
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		LogMajor(COMPONENT_IDFS_ACL, "ckeck_idfs_acl is OVER, returned %d", rc);
		rc = idfs_acl_printf(idfs_acl);
	}

	acldata.naces = idfs_acl->dacl_ace_count;
	if (!acldata.naces) {
		rc = -1;
		goto out;

	}
	/*apply memory for ace*/
	acldata.aces = (fsal_ace_t *) malloc(acldata.naces * sizeof(fsal_ace_t));
	memset(acldata.aces, 0, acldata.naces * sizeof(fsal_ace_t));
	/*acl converted to ace */
	new_count = idfs_acl_2_ace(idfs_acl, &acldata.aces);
	if (new_count < 0) {
		LogMajor(COMPONENT_IDFS_ACL, "idfs_acl_2_ace is error, returned %d", new_count);
		rc = new_count;
		goto out;
	}
	
	acldata.naces = new_count;
	if (unlikely(component_log_level[COMPONENT_IDFS_ACL] >= NIV_DEBUG)){
		rc = ace_printf(acldata);
        	if (rc < 0) {
			LogMajor(COMPONENT_IDFS_ACL,"print ace acl is error, rc:%d", rc);
			goto out;
        	}
	}
	
	acldata.aces = (fsal_ace_t *) gsh_realloc(acldata.aces,
					new_count*sizeof(fsal_ace_t));
	
	attrs->acl = nfs4_acl_new_entry(&acldata, &aclstatus);
	if (attrs->acl == NULL) {
		LogCrit(COMPONENT_IDFS_ACL, "failed to create a new acl entry");
		rc = -EFAULT;
		goto out;
	}

	rc = 0;
	attrs->valid_mask |= ATTR_ACL;
	
out:
	if (idfs_acl) {
		gsh_free(idfs_acl);
		idfs_acl = NULL;
	}
	//if (acldata.aces) {
	//	gsh_free(acldata.aces);
	//	acldata.aces = NULL;
	//}
	//rc = 0;
	return rc;
}


#endif				/* IDFSFS_POSIX_ACL */
