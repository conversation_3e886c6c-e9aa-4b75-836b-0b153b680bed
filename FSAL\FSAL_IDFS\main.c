/*
 * Copyright 漏 2012-2014, CohortFS, LLC.
 * Author: <PERSON> <<EMAIL>>
 *
 * contributeur : <PERSON> <<EMAIL>>
 *		  <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public License
 * as published by the Free Software Foundation; either version 3 of
 * the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA
 * 02110-1301 USA
 *
 * -------------
 */

/**
 * @file FSAL_IDFS/main.c
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @date Wed Oct 22 13:24:33 2014
 *
 * @brief Implementation of FSAL module founctions for Idfs
 *
 * This file implements the module functions for the Idfs FSAL, for
 * initialization, teardown, configuration, and creation of exports.
 */

#include <stdlib.h>
#include <assert.h>
#include "fsal.h"
#include "fsal_types.h"
#include "FSAL/fsal_init.h"
#include "FSAL/fsal_commonlib.h"
#include "fsal_api.h"
#include "internal.h"
#include "abstract_mem.h"
#include "nfs_exports.h"
#include "export_mgr.h"
#include "statx_compat.h"
#include "nfs_core.h"
#include "nfs_fh.h"
#include "sal_functions.h"
#include "FSAL/fsal_commonlib.h"
#include "mdcache.h"

/**
 * The name of this module.
 */
static const char *module_name = "Idfs";
struct idfs_export root_export;
uint32_t conf_version = 0;
bool config_run = false;

pthread_t mon_call_back_conf;

struct conf_vers {
	uint32_t vers;
};
/**
 * Idfs global module object.
 */
struct idfs_fsal_module IdfsFSM = {
	.fsal = {
		.fs_info = {
		#if 0
			.umask = 0,
		#endif
			/* fixed */
			.symlink_support = true,
			.link_support = true,
			.cansettime = true,
			.no_trunc = true,
			.chown_restricted = true,
			.case_preserving = true,
			.maxfilesize = INT64_MAX,
			.maxread = FSAL_MAXIOSIZE,
			.maxwrite = FSAL_MAXIOSIZE,
			.maxlink = 1024,
			.maxnamelen = NAME_MAX,
			.maxpathlen = PATH_MAX,
#ifdef IDFSFS_POSIX_ACL
			.acl_support = FSAL_ACLSUPPORT_ALLOW,
#else				/* IDFSFS_POSIX_ACL */
			.acl_support = 0,
#endif				/* IDFSFS_POSIX_ACL */
			.supported_attrs = IDFS_SUPPORTED_ATTRS,
		#ifdef USE_FSAL_IDFS_SETLK
			.lock_support = true,
			.lock_support_async_block = false,
		#endif
			.unique_handles = true,
			.homogenous = true,
		#ifdef USE_FSAL_IDFS_LL_DELEGATION
			.delegations = FSAL_OPTION_FILE_READ_DELEG,
		#endif
			.readdir_plus = true,
			.xattr_support = true,
			.expire_time_parent = -1,
		}
	}
};

static struct config_item idfs_items[] = {
	CONF_ITEM_PATH("idfs_conf", 1, MAXPATHLEN, NULL,
		idfs_fsal_module, conf_path),
	CONF_ITEM_MODE("umask", 0,
			idfs_fsal_module, fsal.fs_info.umask),
	CONFIG_EOL
};

static struct config_block idfs_block = {
	.dbus_interface_name = "org.gnfs.nfsd.config.fsal.idfs",
	.blk_desc.name = "Idfs",
	.blk_desc.type = CONFIG_BLOCK,
	.blk_desc.u.blk.init = noop_conf_init,
	.blk_desc.u.blk.params = idfs_items,
	.blk_desc.u.blk.commit = noop_conf_commit
};

/* Module methods
 */

/* init_config
 * must be called with a reference taken (via lookup_fsal)
 */

static fsal_status_t init_config(struct fsal_module *module_in,
				 config_file_t config_struct,
				 struct config_error_type *err_type)
{
	struct idfs_fsal_module *myself =
	    container_of(module_in, struct idfs_fsal_module, fsal);

	(void) prepare_for_stats(module_in);

	LogDebug(COMPONENT_FSAL,
		 "Idfs module setup.");

	(void) load_config_from_parse(config_struct,
				      &idfs_block,
				      myself,
				      true,
				      err_type);
	if (!config_error_is_harmless(err_type))
		return fsalstat(ERR_FSAL_INVAL, 0);

	display_fsinfo(&myself->fsal);
	return fsalstat(ERR_FSAL_NO_ERROR, 0);
}

#ifdef USE_FSAL_IDFS_LL_LOOKUP_ROOT
static fsal_status_t find_idfsfs_root(struct idfs_mount_info *cmount,
					IdfsInode **pi)
{
	return idfs2fsal_error(idfs_ll_lookup_root(cmount, pi));
}
#else /* USE_FSAL_IDFS_LL_LOOKUP_ROOT */
static fsal_status_t find_idfsfs_root(struct idfs_mount_info *cmount,
					IdfsInode **pi)
{
	struct stat st;

	return idfs2fsal_error(idfs_ll_walk(cmount, "/", pi, &st));
}
#endif /* USE_FSAL_IDFS_LL_LOOKUP_ROOT */

static struct config_item export_params[] = {
	CONF_ITEM_NOOP("name"),
	CONF_ITEM_STR("user_id", 0, MAXUIDLEN, NULL, idfs_export, user_id),
	CONF_ITEM_STR("filesystem", 0, NAME_MAX, NULL, idfs_export, fs_name),
	CONF_ITEM_STR("secret_access_key", 0, MAXSECRETLEN, NULL, idfs_export,
			secret_key),
	CONF_ITEM_STR("sec_label_xattr", 0, 256, "security.selinux",
			idfs_export, sec_label_xattr),
	CONFIG_EOL
};

static struct config_block export_param_block = {
	.dbus_interface_name = "org.gnfs.nfsd.config.fsal.idfs-export%d",
	.blk_desc.name = "FSAL",
	.blk_desc.type = CONFIG_BLOCK,
	.blk_desc.u.blk.init = noop_conf_init,
	.blk_desc.u.blk.params = export_params,
	.blk_desc.u.blk.commit = noop_conf_commit
};

#ifdef USE_FSAL_IDFS_LL_DELEGATION
static void enable_delegations(struct idfs_export *export)
{
	struct export_perms *export_perms = &op_ctx->ctx_export->export_perms;

	if (export_perms->options & EXPORT_OPTION_DELEGATIONS) {
		/*
		 * Gnfs will time out delegations when the recall fails
		 * for two lease periods. We add just a little bit above that
		 * as a scheduling fudge-factor.
		 *
		 * The idea here is to make this long enough to give gnfs
		 * a chance to kick out a misbehaving client, but shorter
		 * than idfs cluster-wide DMS session timeout.
		 *
		 * Exceeding the DMS session timeout may result in the client
		 * (gnfs) being blacklisted in the cluster. Fixing that can
		 * require a long wait and/or administrative intervention.
		 */
		unsigned int dt = nfs_param.nfsv4_param.lease_lifetime * 2 + 5;
		int idfs_status;

		LogDebug(COMPONENT_FSAL, "Setting deleg timeout to %u", dt);
		idfs_status = idfs_set_deleg_timeout(export->cmount, dt);
		if (idfs_status != 0) {
			export_perms->options &= ~EXPORT_OPTION_DELEGATIONS;
			LogWarn(COMPONENT_FSAL,
				"Unable to set delegation timeout for %s. Disabling delegation support: %d",
				CTX_FULLPATH(op_ctx), idfs_status);
		}
	}
}
#else /* !USE_FSAL_IDFS_LL_DELEGATION */
static inline void enable_delegations(struct idfs_export *export)
{
}
#endif /* USE_FSAL_IDFS_LL_DELEGATION */

#ifdef USE_FSAL_IDFS_RECLAIM_RESET
#define RECLAIM_UUID_PREFIX		"gnfs-"
static int reclaim_reset(struct idfs_export *export)
{
	int		idfs_status;
	char		*nodeid, *uuid;
	size_t		len;

	/*
	 * Set long timeout for the session to ensure that DMS doesn't lose
	 * state before server can come back and do recovery.
	 */
	//idfs_set_session_timeout(export->cmount, 300);
	if (nfs_param.core_param.idfs_session_timeout >= 0){
		idfs_set_session_timeout(export->cmount, nfs_param.core_param.idfs_session_timeout);
		LogDebug(COMPONENT_FSAL, "export_id=%d cmount=%p  set session timeout %d.", export->export.export_id, export->cmount, nfs_param.core_param.idfs_session_timeout);
	}
	/*
	 * For the uuid here, we just use whatever gnfs- + whatever
	 * nodeid the recovery backend reports.
	 */
	idfs_status = nfs_recovery_get_nodeid(&nodeid);
	if (idfs_status != 0) {
		LogEvent(COMPONENT_FSAL, "couldn't get nodeid: %d", errno);
		return idfs_status;
	}

	len = strlen(RECLAIM_UUID_PREFIX) + strlen(nodeid) + 1 + 4 + 1;
	uuid = gsh_malloc(len);
	(void) snprintf(uuid, len, RECLAIM_UUID_PREFIX "%s-%4.4hx", nodeid,
			export->export.export_id);

	/* If this fails, log a message but soldier on */
	LogDebug(COMPONENT_FSAL, "Issuing reclaim reset for %s", uuid);
	idfs_status = idfs_start_reclaim(export->cmount, uuid,
						IDFS_RECLAIM_RESET);
	if (idfs_status)
		LogEvent(COMPONENT_FSAL, "start_reclaim failed: %d",
				idfs_status);
	idfs_finish_reclaim(export->cmount);
	idfs_set_uuid(export->cmount, uuid);
	gsh_free(nodeid);
	gsh_free(uuid);
	return 0;
}
#undef RECLAIM_UUID_PREFIX
#else
static inline int reclaim_reset(struct idfs_export *export)
{
	return 0;
}
#endif

#ifdef USE_FSAL_IDFS_GET_FS_CID
static int select_filesystem(struct idfs_export *export)
{
	int idfs_status;

	if (export->fs_name) {
		idfs_status = idfs_select_filesystem(export->cmount,
						     export->fs_name);
		if (idfs_status != 0) {
			LogCrit(COMPONENT_FSAL,
				"Unable to set filesystem to %s.",
				export->fs_name);
			return idfs_status;
		}
	}
	return 0;
}
#else /* USE_FSAL_IDFS_GET_FS_CID */
static int select_filesystem(struct idfs_export *export)
{
	if (export->fs_name) {
		LogCrit(COMPONENT_FSAL,
			"This libidfsfs version doesn't support named filesystems.");
		return -EINVAL;
	}
	return 0;
}
#endif /* USE_FSAL_IDFS_GET_FS_CID */

#ifdef USE_FSAL_IDFS_REGISTER_CALLBACKS
static void ino_release_cb(void *handle, vinodeno_t vino)
{
	struct idfs_export *export = handle;
	struct idfs_handle_key key;
	struct gsh_buffdesc fh_desc;

	LogDebug(COMPONENT_FSAL,
		 "libidfsfs asking to release 0x%lx:0x%lx:0x%lx",
		 export->fscid, vino.snapid.val, vino.ino.val);
	key.hhdl.chk_ino = vino.ino.val;
	key.hhdl.chk_snap = vino.snapid.val;
	key.hhdl.chk_fscid = export->fscid;
	key.export_id = export->export.export_id;
	fh_desc.addr = &key;
	fh_desc.len = sizeof(key);

	export->export.up_ops->try_release(export->export.up_ops, &fh_desc, 0);
}

/* Callback for inode invalidation. This callback is triggered when ceph client
 * cache is invalidated due to file attribute change */
static void ino_invalidate_cb(void *handle, vinodeno_t vino,
		int64_t offset, int64_t len)
{
	struct idfs_export *export = handle;
	struct idfs_handle_key key;
	struct gsh_buffdesc fh_desc;

	LogDebug(COMPONENT_FSAL,
		 "libidfsfs asking to invalidate 0x%lx:0x%lx:0x%lx",
		 export->fscid, vino.snapid.val, vino.ino.val);
	key.hhdl.chk_ino = vino.ino.val;
	key.hhdl.chk_snap = vino.snapid.val;
	key.hhdl.chk_fscid = export->fscid;
	key.export_id = export->export.export_id;
	fh_desc.addr = &key;
	fh_desc.len = sizeof(key);
	export->export.up_ops->invalidate(
				export->export.up_ops, &fh_desc,
				FSAL_UP_INVALIDATE_CACHE);
}

static void dentry_ino_cb(void *handle, vinodeno_t dirvino,
					 vinodeno_t vino, const char *name,
					 size_t len)
{
	struct idfs_export *export = handle;
	struct idfs_handle_key file_key, dir_key;
	struct gsh_buffdesc file_fh_desc, dir_fh_desc;
	
	LogWarn(COMPONENT_FSAL,
		 "libidfsfs asking to invalidate dentry '%.*s' (INO 0x%lx:0x%lx:0x%lx) in DIR 0x%lx:0x%lx:0x%lx",
		 (int)len, name, export->fscid, vino.snapid.val, vino.ino.val,
		 export->fscid, dirvino.snapid.val, dirvino.ino.val);
	
	memset(&file_key, 0, sizeof(file_key));
	file_key.hhdl.chk_ino = vino.ino.val;
	file_key.hhdl.chk_snap = vino.snapid.val;
	file_key.hhdl.chk_fscid = export->fscid;
	strncpy(file_key.hhdl.chk_fsid, nfs_param.core_param.gnfs_fh_fsid, strlen(nfs_param.core_param.gnfs_fh_fsid));
	file_key.hhdl.chk_vision = FH_VERSION;
	file_key.export_id = export->export.export_id;
	file_fh_desc.addr = &file_key;
	file_fh_desc.len = sizeof(file_key);

	memset(&dir_key, 0, sizeof(dir_key));
	dir_key.hhdl.chk_ino = dirvino.ino.val;
	dir_key.hhdl.chk_snap = dirvino.snapid.val;
	dir_key.hhdl.chk_fscid = export->fscid;
	strncpy(dir_key.hhdl.chk_fsid, nfs_param.core_param.gnfs_fh_fsid, strlen(nfs_param.core_param.gnfs_fh_fsid));
	dir_key.hhdl.chk_vision = FH_VERSION;
	dir_key.export_id = export->export.export_id;
	dir_fh_desc.addr = &dir_key;
	dir_fh_desc.len = sizeof(dir_key);


	LogWarn(COMPONENT_FSAL, "MDCACHE dentry for file entry, name='%s', async=%s", name, 
		nfs_param.core_param.async_dentry_enable ? "true" : "false");
	
	LogWarn(COMPONENT_FSAL, "vino=%p, file_fh_desc=%p, file_fh_desc->addr=%p, export->export.up_ops=%p", 
		&vino, &file_fh_desc, file_fh_desc.addr, export->export.up_ops);

	if (nfs_param.core_param.async_dentry_enable) {
		int ret = put_ino_queue(export->export.up_ops, &file_fh_desc, &dir_fh_desc, name);
		if (ret == 0) {
			return;
		}
	}
	
	export->export.up_ops->dentry(
				export->export.up_ops, &dir_fh_desc, &file_fh_desc,
				name, 0);

	/*
	LogWarn(COMPONENT_FSAL, "try_release file");
	export->export.up_ops->try_release(
				export->export.up_ops, &file_fh_desc, 0);

	LogWarn(COMPONENT_FSAL, "invalidate dir");
	export->export.up_ops->invalidate(
				export->export.up_ops, &dir_fh_desc,
				FSAL_UP_INVALIDATE_CONTENT |
				FSAL_UP_INVALIDATE_DIR_CHUNKS |
				FSAL_UP_INVALIDATE_DIR_POPULATED);
	*/
}


static mode_t umask_cb(void *handle)
{
	mode_t umask = IdfsFSM.fsal.fs_info.umask;

	LogDebug(COMPONENT_FSAL,
		"libidfsfs set umask = %04o by umask callback", umask);
	return umask;
}

static void register_callbacks(struct idfs_export *export)
{
	struct idfs_client_callback_args args = {
					.handle = export,
					.ino_cb = ino_invalidate_cb,
					.dentry_cb = dentry_ino_cb,
					.ino_release_cb = ino_release_cb,
					.umask_cb = umask_cb
				};
	idfs_ll_register_callbacks(export->cmount, &args);
}

void* nfs_modify_export(void* arg)
{
	struct conf_vers *nfs_conf_ver = arg;
	char stokchar[32];
	char add_export_cmd[128];
	char rem_export_cmd[128];
	char reload_export_cmd[128];
	char *ch_id = NULL;
	char *export_type = NULL;
	int ret1 = 0;
	uint32_t vers = nfs_conf_ver->vers;
	LogEvent(COMPONENT_FSAL, "Export_function begin: vers = %d, vers p_addr:%p, nfs_conf_ver = %d, nfs_conf_ver p_addr:%p, conf_version = %d, config_run = %d", vers, &vers, nfs_conf_ver->vers, &nfs_conf_ver->vers, conf_version, config_run);
	if(vers != conf_version && config_run) {

		FILE *pipe = popen("idfs config-key get config-client/gnfs/export_id","r");

		if(!pipe){
			LogEvent(COMPONENT_FSAL,"Failed to open idfs config-key get config-client/gnfs/export_id");
			config_run = true;
			mem_free(nfs_conf_ver, sizeof(*nfs_conf_ver));
			nfs_conf_ver = NULL;
			return NULL;
		}
		while(fgets(stokchar,sizeof(stokchar),pipe) != NULL) {
			LogEvent(COMPONENT_FSAL,"get config-client/gnfs/export_id return, stokchar = %s", stokchar);
			ch_id = strtok(stokchar,"=");
			export_type = strtok(NULL,"=");
		}

		system("idfs config-key get config-client/gnfs/gnfs.conf -o /etc/gnfs/gnfs.zip");
		
		LogEvent(COMPONENT_INIT, "unzip /etc/gnfs/gnfs.zip begin");
		int ret2 = system("unzip -p /etc/gnfs/gnfs.zip > /etc/gnfs/gnfs.conf");
		if(ret2 != 0) {
			LogEvent(COMPONENT_INIT,"unzip /etc/gnfs/gnfs.zip failed.");
		}else{
			LogEvent(COMPONENT_INIT, "unzip /etc/gnfs/gnfs.zip successfully");
		}
		if(export_type != NULL && ch_id != NULL &&  atoi(ch_id)!= 0) {
			snprintf(add_export_cmd,sizeof(add_export_cmd),
				"gnfs_mgr add export  /etc/gnfs/gnfs.conf EXPORT\\(Export_ID=%d\\)",atoi(ch_id));
			snprintf(rem_export_cmd,sizeof(rem_export_cmd),
				"gnfs_mgr remove export %d",atoi(ch_id));
			snprintf(reload_export_cmd,sizeof(reload_export_cmd),
				"gnfs_mgr update export  /etc/gnfs/gnfs.conf EXPORT\\(Export_ID=%d\\)",atoi(ch_id));
			LogEvent(COMPONENT_FSAL,"export_id = %d, export_type = %s", atoi(ch_id), export_type);

			if(!strcmp(export_type,"add")) {
				ret1 = system(add_export_cmd);
				if (ret1 < 0) {
					LogEvent(COMPONENT_FSAL,"add export failed ret = %d, export_id = %d",ret1,atoi(ch_id));
				}
			} else if(!strcmp(export_type,"remove")) {
				ret1 = system(rem_export_cmd);
				if (ret1 < 0) {
					LogEvent(COMPONENT_FSAL,"remove export failed ret = %d, export_id = %d",ret1,atoi(ch_id));
				}
			} else if(!strcmp(export_type,"edit")) {
				ret1 = system(reload_export_cmd);
				if (ret1 < 0) {
					LogEvent(COMPONENT_FSAL,"edit export failed ret1 = %d, export_id = %d",ret1,atoi(ch_id));
				}          
			}
		}
		if(ch_id != NULL && atoi(ch_id) == 0) {
			LogEvent(COMPONENT_FSAL,"export_id = %d, gnfs.conf has been updated.", atoi(ch_id));
		}
		pclose(pipe);
		conf_version = vers;
	}
	config_run = true;
	LogEvent(COMPONENT_FSAL, "Export_function end: vers = %d, vers p_addr:%p, nfs_conf_ver = %d, nfs_conf_ver p_addr:%p, conf_version = %d, config_run = %d", vers, &vers, nfs_conf_ver->vers, &nfs_conf_ver->vers, conf_version, config_run);
	mem_free(nfs_conf_ver, sizeof(*nfs_conf_ver));
	nfs_conf_ver = NULL;
	return NULL;

}

void ll_config_cb(uint32_t vers)
{
	struct conf_vers *nfs_conf_ver = mem_zalloc(sizeof(*nfs_conf_ver));
	nfs_conf_ver->vers = vers;
	LogEvent(COMPONENT_THREAD, "mon_call_back_conf thread begining, vers:%d, vers p_addr:%p, nfs_conf_vers:%d, nfs_conf_vers p_addr:%p", vers, &vers, nfs_conf_ver->vers, &nfs_conf_ver->vers);
	int rc = 0;
	rc = pthread_create(&mon_call_back_conf, 0, nfs_modify_export, nfs_conf_ver);
	if (rc != 0) {
		LogFatal(COMPONENT_THREAD,
			 "Could not create mon_call_back_conf thread, error = %d (%s), vers:%d, vers p_addr:%p, nfs_conf_vers:%d, nfs_conf_vers p_addr:%p",
			 errno, strerror(errno), vers, &vers, nfs_conf_ver->vers, &nfs_conf_ver->vers);
		if(nfs_conf_ver){
			mem_free(nfs_conf_ver, sizeof(*nfs_conf_ver));
			nfs_conf_ver = NULL;
		}
	}
}

int get_instance_id(struct idfs_export *export)
{
	char str[64];
	FILE *file = NULL;

	uint64_t id = idfs_get_instance_id(export->cmount);
	LogEvent(COMPONENT_FSAL, "idfs_get_instance_id = %lu",id);

	sprintf(str,"%lu",id);

	file = fopen("/etc/gnfs/clientid","w");

	if(file != NULL) { 
		fprintf(file,"%s\n",str);
		fclose(file);
	} else {
		LogEvent(COMPONENT_FSAL, "fopen file failed");
	}
	return 0;

}

#else /* USE_FSAL_IDFS_REGISTER_CALLBACKS */
static void register_callbacks(struct idfs_export *export)
{
	LogWarnOnce(COMPONENT_FSAL,
		    "This libidfsfs does not support registering callbacks. Gnfs will be unable to respond to DMS cache pressure.");
}
#endif /* USE_FSAL_IDFS_REGISTER_CALLBACKS */

int extract_fh_fsid_part(const char* input, char *output, size_t output_size){
	const char *last_dash = strrchr(input, '-');
	if(last_dash != NULL){
		size_t len_to_copy = strlen(last_dash + 1) < output_size ? strlen(last_dash + 1) : output_size;
		strncpy(output, last_dash + 1, len_to_copy);
		output[output_size] = '\0';
		return 0;
	}
	return -1;
}

/**
 * @brief Create a new export under this FSAL
 *
 * This function creates a new export object for the Idfs FSAL.
 *
 * @todo ACE: We do not handle re-exports of the same cluster in a
 * sane way.  Currently we create multiple handles and cache objects
 * pointing to the same one.  This is not necessarily wrong, but it is
 * inefficient.  It may also not be something we expect to use enough
 * to care about.
 *
 * @param[in]     module_in  The supplied module handle
 * @param[in]     path       The path to export
 * @param[in]     options    Export specific options for the FSAL
 * @param[in,out] list_entry Our entry in the export list
 * @param[in]     next_fsal  Next stacked FSAL
 * @param[out]    pub_export Newly created FSAL export object
 *
 * @return FSAL status.
 */
 static fsal_status_t create_subexport(struct fsal_module *module_in,void *parse_node,
									struct config_error_type *err_type, const struct fsal_up_vector *up_ops)
{
	fsal_status_t root_status;
	fsal_status_t status = {ERR_FSAL_NO_ERROR, 0};
	struct idfs_export *idfs_export = NULL;

	//zhangshuai jicai begin
	if(root_export.cmount == NULL)
	{
		root_status= module_in->m_ops.create_root_export(module_in,parse_node,err_type,up_ops);
		if(FSAL_IS_ERROR(root_status))
		{
			status.major = ERR_FSAL_SERVERFAULT;
			LogFatal(COMPONENT_CONFIG,
				"Could not create export to '/.' root_status is %d.",root_status.major);
			goto error;
		}
	}
	
	bool initialized = false;
	idfs_export = gsh_calloc(1, sizeof(struct idfs_export));
	if (idfs_export == NULL){
		status.major = ERR_FSAL_INVAL;
		LogCrit(COMPONENT_FSAL,
			"Unable to calloc idfs_export");
          	goto error;
        }
	idfs_export->is_root_export = false;
	
	fsal_export_init(&idfs_export->export);
	export_ops_init(&idfs_export->export.exp_ops);
	idfs_export->export.up_ops = up_ops;
	
	initialized = true;

	//zhangshuai jicai begin
	
	idfs_export->cmount = root_export.cmount;
	idfs_export->root = root_export.root;
	//zhangshuai end
	
	if(fsal_attach_export(module_in, &idfs_export->export.exports) != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
				"Unable to attach export for %s.",
				CTX_FULLPATH(op_ctx));
		goto error;
	}
	idfs_export->export.fsal = module_in;
	op_ctx->fsal_export= &idfs_export->export;
	return status;
error:
	if (idfs_export) {
		gsh_free(idfs_export);
	}
	if (initialized)
		initialized = false;
	return status;
}
 static fsal_status_t create_root_export(struct fsal_module *module_in,
				   void *parse_node,
				   struct config_error_type *err_type,
				   const struct fsal_up_vector *up_ops)
{
	/* The status code to return */
	fsal_status_t status = { ERR_FSAL_NO_ERROR, 0 };
	
	root_export.is_root_export = true;
	
	/* The internal export object */
	struct idfs_export *export = &root_export;
	/* The 'private' root handle */
	struct idfs_handle *handle = NULL;
	/* Root inode */
	struct IdfsInode *i = NULL;
	/* Stat for root */
	struct idfs_statx stx;
	/* Return code */
	int rc;
	/* Return code from Idfs calls */
	int idfs_status;
	LogDebug(COMPONENT_FSAL, "Root export %p.", &root_export);
	fsal_export_init(&export->export);
	export_ops_init(&export->export.exp_ops);

	/* get params for this export, if any */
	if (parse_node) {
		rc = load_config_from_node(parse_node,
					   &export_param_block,
					   export,
					   true,
					   err_type);
		if (rc != 0) {
			/*add for bug 65300,no calloc, should not free memory */
			//gsh_free(export);
			return fsalstat(ERR_FSAL_INVAL, 0);
		}
	}

	/* allocates idfs_mount_info */
	/*add by zhanghao for client log output*/
	char strpid[1024];
	sprintf(strpid, "gnfs-%d", getpid());
	idfs_status = idfs_create(&export->cmount, strpid);
	//idfs_status = idfs_create(&export->cmount, export->user_id);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to create Idfs handle for /.");
		goto error;
	}

	idfs_status = idfs_conf_read_file(export->cmount, IdfsFSM.conf_path);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to read Idfs configuration for /. ");
		goto error;
	}

	if (export->secret_key) {
		idfs_status = idfs_conf_set(export->cmount, "key",
					    export->secret_key);
		if (idfs_status) {
			status.major = ERR_FSAL_INVAL;
			LogCrit(COMPONENT_FSAL,
				"Unable to set Idfs secret key for /. : %d",idfs_status);
			goto error;
		}
	}

	/*
	 * Workaround for broken libidfsfs that doesn't handle the path
	 * given in idfs_mount properly. Should be harmless for fixed
	 * libidfsfs as well (see http://tracker.idfs.com/issues/18254).
	 */
	idfs_status = idfs_conf_set(export->cmount, "client_mountpoint",
				    "/");
	if (idfs_status) {
		status.major = ERR_FSAL_INVAL;
		LogCrit(COMPONENT_FSAL,
			"Unable to set Idfs client_mountpoint for /. : %d", idfs_status);
		goto error;
	}

	idfs_status = idfs_conf_set(export->cmount, "fuse_default_permissions", "true");
	if (idfs_status) {
		status.major = ERR_FSAL_INVAL;
		LogCrit(COMPONENT_FSAL,
			"Unable to set fuse_default_permissions for %s: %d",
			"true", idfs_status);
		goto error;
	}
	
	idfs_status = idfs_conf_set(export->cmount, "client_permissions", "false");
	if (idfs_status) {
		status.major = ERR_FSAL_INVAL;
		LogCrit(COMPONENT_FSAL,
			"Unable to set client_permissions for %s: %d",
			"false", idfs_status);
		goto error;
	}

	char fsid[1024];
	memset(fsid, 0, sizeof(fsid));
	idfs_status = idfs_conf_get(export->cmount, "fsid", fsid, 1024);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to get fsid");
		goto error;
	}
	LogCrit(COMPONENT_FSAL,"Fsid is %s", fsid);
	char gnfs_fh_fsid[5];
	if(extract_fh_fsid_part(fsid, gnfs_fh_fsid, sizeof(gnfs_fh_fsid)) < 0){
		LogCrit(COMPONENT_FSAL,"result is %s", gnfs_fh_fsid);		
		goto error;		
	}
	strcpy(nfs_param.core_param.gnfs_fh_fsid, gnfs_fh_fsid);
	LogCrit(COMPONENT_FSAL,"gnfs_fh_fsid is %s len %lu,  core_param.gnfs_fh_fsid is %s len %lu",
		gnfs_fh_fsid, strlen(gnfs_fh_fsid), nfs_param.core_param.gnfs_fh_fsid, strlen(nfs_param.core_param.gnfs_fh_fsid));
	idfs_status = idfs_init(export->cmount);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to init Idfs handle for /..");
		goto error;
	}

	register_callbacks(export);

	idfs_status = select_filesystem(export);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		goto error;
	}

	idfs_status = reclaim_reset(export);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to do reclaim_reset for /. .");
		goto error;
	}

	idfs_status = idfs_mount(export->cmount, NULL);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to mount Idfs cluster for /. .");
		goto error;
	}

#ifdef USE_FSAL_IDFS_GET_FS_CID
	/* Fetch fscid for use in filehandles */
	export->fscid = idfs_get_fs_cid(export->cmount);
	if (export->fscid < 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Error getting fscid for %s.", export->fs_name);
		goto error;
	}
#endif /* USE_FSAL_IDFS_GET_FS_CID */

	enable_delegations(export);

	export->export.fsal = module_in;
	export->export.up_ops = up_ops;

	LogDebug(COMPONENT_FSAL, "Idfs module export /. .");

	status = find_idfsfs_root(export->cmount, &i);
	if (FSAL_IS_ERROR(status))
		goto error;

	rc = fsal_idfs_ll_getattr(export->cmount, i, &stx,
				IDFS_STATX_HANDLE_MASK, &op_ctx->creds);
	if (rc < 0) {
		status = idfs2fsal_error(rc);
		goto error;
	}

	construct_handle(&stx, i, export, &handle);

	export->root = handle;
	#if 0
	if (fsal_attach_export(module_in, &export->export.exports) != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to attach export for /. .");
		goto error;
	}
	#endif

	get_instance_id(export);
	if (nfs_param.core_param.enable_conf_global){	
		if (strcmp(CTX_FULLPATH(op_ctx),"/") == 0) {
			idfs_set_conf_clientcb(export->cmount,ll_config_cb,"gnfs");
			LogEvent(COMPONENT_FSAL, "idfs_set_conf_clientcb register ll_config_cb: %p", ll_config_cb);
		}
	}

	return status;
 error:
	if (i)
		idfs_ll_put(export->cmount, i);

	if (export->cmount)
		idfs_shutdown(export->cmount);
	/*add for bug 65300,no calloc, should not free memory */
	//gsh_free(export);
	LogCrit(COMPONENT_FSAL, "create root export is error, idfs_status:%d.", idfs_status);
	return status;
}
static fsal_status_t destory_root_export(void)
{
	/* The status code to return */
	fsal_status_t status = { ERR_FSAL_NO_ERROR, 0 };

	/* The private, expanded export */
	struct idfs_export *export = &root_export;
	op_ctx->fsal_export = &export->export;

	LogEvent(COMPONENT_FSAL, "begin root_export shutdown,export %p, cmount %p", export, export->cmount);

	deconstruct_handle(export->root);
	export->root = 0;

	fsal_detach_export(export->export.fsal, &export->export.exports);
	free_export_ops(&export->export);

	idfs_shutdown(export->cmount);
	export->cmount = NULL;
	export = NULL;
	LogEvent(COMPONENT_FSAL, "done root_export shutdown");


	return status;
}
static fsal_status_t create_export(struct fsal_module *module_in,
				   void *parse_node,
				   struct config_error_type *err_type,
				   const struct fsal_up_vector *up_ops)
{
	/* The status code to return */
	fsal_status_t status = { ERR_FSAL_NO_ERROR, 0 };
	/* The internal export object */
	struct idfs_export *export = gsh_calloc(1, sizeof(struct idfs_export));
	/* The 'private' root handle */
	struct idfs_handle *handle = NULL;
	/* Root inode */
	struct IdfsInode *i = NULL;
	/* Stat for root */
	struct idfs_statx stx;
	/* Return code */
	int rc;
	/* Return code from Idfs calls */
	int idfs_status;

	fsal_export_init(&export->export);
	export_ops_init(&export->export.exp_ops);

	/* get params for this export, if any */
	if (parse_node) {
		rc = load_config_from_node(parse_node,
					   &export_param_block,
					   export,
					   true,
					   err_type);
		if (rc != 0) {
			gsh_free(export);
			return fsalstat(ERR_FSAL_INVAL, 0);
		}
	}

	/* allocates idfs_mount_info */
	/*add by zhanghao for client log output*/
	char strpid[1024];
	sprintf(strpid, "gnfs-%d-%d", getpid(), op_ctx->ctx_export->export_id);
	idfs_status = idfs_create(&export->cmount, strpid);
	//idfs_status = idfs_create(&export->cmount, export->user_id);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to create Idfs handle for %s.",
			CTX_FULLPATH(op_ctx));
		goto error;
	}

	idfs_status = idfs_conf_read_file(export->cmount, IdfsFSM.conf_path);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to read Idfs configuration for %s.",
			CTX_FULLPATH(op_ctx));
		goto error;
	}

	if (export->secret_key) {
		idfs_status = idfs_conf_set(export->cmount, "key",
					    export->secret_key);
		if (idfs_status) {
			status.major = ERR_FSAL_INVAL;
			LogCrit(COMPONENT_FSAL,
				"Unable to set Idfs secret key for %s: %d",
				CTX_FULLPATH(op_ctx), idfs_status);
			goto error;
		}
	}

	/*
	 * Workaround for broken libidfsfs that doesn't handle the path
	 * given in idfs_mount properly. Should be harmless for fixed
	 * libidfsfs as well (see http://tracker.idfs.com/issues/18254).
	 */
	idfs_status = idfs_conf_set(export->cmount, "client_mountpoint",
				    CTX_FULLPATH(op_ctx));
	if (idfs_status) {
		status.major = ERR_FSAL_INVAL;
		LogCrit(COMPONENT_FSAL,
			"Unable to set Idfs client_mountpoint for %s: %d",
			CTX_FULLPATH(op_ctx), idfs_status);
		goto error;
	}

        idfs_status = idfs_conf_set(export->cmount, "fuse_default_permissions", "true");
        if (idfs_status) {
                status.major = ERR_FSAL_INVAL;
                LogCrit(COMPONENT_FSAL,
                        "Unable to set fuse_default_permissions for %s: %d",
                        "true", idfs_status);
                goto error;
        }

        idfs_status = idfs_conf_set(export->cmount, "client_permissions", "false");
        if (idfs_status) {
                status.major = ERR_FSAL_INVAL;
                LogCrit(COMPONENT_FSAL,
                        "Unable to set client_permissions for %s: %d",
                        "false", idfs_status);
                goto error;
        }

	char fsid[1024];
	memset(fsid, 0, sizeof(fsid));
	idfs_status = idfs_conf_get(export->cmount, "fsid", fsid, 1024);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to get fsid");
		goto error;
	}
	LogCrit(COMPONENT_FSAL,"Fsid is %s", fsid);
	char gnfs_fh_fsid[5];
	if(extract_fh_fsid_part(fsid, gnfs_fh_fsid, sizeof(gnfs_fh_fsid)) < 0){
		LogCrit(COMPONENT_FSAL,"result is %s", gnfs_fh_fsid);		
		goto error;		
	}
	strcpy(nfs_param.core_param.gnfs_fh_fsid, gnfs_fh_fsid);
	LogCrit(COMPONENT_FSAL,"gnfs_fh_fsid is %s len %lu,  core_param.gnfs_fh_fsid is %s len %lu", 
		gnfs_fh_fsid, strlen(gnfs_fh_fsid), nfs_param.core_param.gnfs_fh_fsid, strlen(nfs_param.core_param.gnfs_fh_fsid));

	idfs_status = idfs_init(export->cmount);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to init Idfs handle for %s.",
			CTX_FULLPATH(op_ctx));
		goto error;
	}

	register_callbacks(export);

	idfs_status = select_filesystem(export);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		goto error;
	}

	idfs_status = reclaim_reset(export);
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to do reclaim_reset for %s.",
			CTX_FULLPATH(op_ctx));
		goto error;
	}

	idfs_status = idfs_mount(export->cmount, CTX_FULLPATH(op_ctx));
	if (idfs_status != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to mount Idfs cluster for %s.",
			CTX_FULLPATH(op_ctx));
		goto error;
	}

#ifdef USE_FSAL_IDFS_GET_FS_CID
	/* Fetch fscid for use in filehandles */
	export->fscid = idfs_get_fs_cid(export->cmount);
	if (export->fscid < 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Error getting fscid for %s.", export->fs_name);
		goto error;
	}
#endif /* USE_FSAL_IDFS_GET_FS_CID */

	enable_delegations(export);

	export->export.fsal = module_in;
	export->export.up_ops = up_ops;

	LogDebug(COMPONENT_FSAL, "Idfs module export %s.",
		 CTX_FULLPATH(op_ctx));

	status = find_idfsfs_root(export->cmount, &i);
	if (FSAL_IS_ERROR(status))
		goto error;

	rc = fsal_idfs_ll_getattr(export->cmount, i, &stx,
				  IDFS_STATX_HANDLE_MASK, &op_ctx->creds);
	if (rc < 0) {
		status = idfs2fsal_error(rc);
		goto error;
	}

	construct_handle(&stx, i, export, &handle);

	export->root = handle;
	op_ctx->fsal_export = &export->export;

	if (fsal_attach_export(module_in, &export->export.exports) != 0) {
		status.major = ERR_FSAL_SERVERFAULT;
		LogCrit(COMPONENT_FSAL,
			"Unable to attach export for %s.",
			CTX_FULLPATH(op_ctx));
		goto error;
	}

	get_instance_id(export);
	if (nfs_param.core_param.enable_conf_global){
		if (strcmp(CTX_FULLPATH(op_ctx),"/") == 0) {
			idfs_set_conf_clientcb(export->cmount,ll_config_cb,"gnfs");
			LogEvent(COMPONENT_FSAL, "idfs_set_conf_clientcb register ll_config_cb: %p", ll_config_cb);
		}
	}
	
	return status;
 error:
	if (i)
		idfs_ll_put(export->cmount, i);

	if (export->cmount)
		idfs_shutdown(export->cmount);
	gsh_free(export);

	return status;
}

/**
 * @brief Initialize and register the FSAL
 *
 * This function initializes the FSAL module handle, being called
 * before any configuration or even mounting of a Idfs cluster.  It
 * exists solely to produce a properly constructed FSAL module
 * handle.
 */

MODULE_INIT void init(void)
{
	struct fsal_module *myself = &IdfsFSM.fsal;

	LogDebug(COMPONENT_FSAL,
		 "Idfs module registering.");

	if (register_fsal(myself, module_name, FSAL_MAJOR_VERSION,
			  FSAL_MINOR_VERSION, FSAL_ID_IDFS) != 0) {
		/* The register_fsal function prints its own log
		   message if it fails */
		LogCrit(COMPONENT_FSAL,
			"Idfs module failed to register.");
	}

	/* Set up module operations */
#ifdef IDFS_PNFS
	myself->m_ops.fsal_pnfs_ds_ops = pnfs_ds_ops_init;
#endif				/* IDFS_PNFS */
	myself->m_ops.create_export = create_export;
	/*add by zhangshuai for ceph_mount 20210422*/
	myself->m_ops.create_subexport = create_subexport;
	myself->m_ops.create_root_export = create_root_export;
	myself->m_ops.destory_root_export = destory_root_export;
	/*add by zhangshuai for ceph_mount 20210422*/

	myself->m_ops.init_config = init_config;

#ifdef USE_DBUS
		myself->m_ops.fsal_extract_stats = fsal_idfs_extract_stats;
#endif
		myself->m_ops.fsal_reset_stats = fsal_idfs_reset_stats;

	/* Initialize the fsal_obj_handle ops for FSAL IDFS */
	handle_ops_init(&IdfsFSM.handle_ops);
}

/**
 * @brief Release FSAL resources
 *
 * This function unregisters the FSAL and frees its module handle.
 * The Idfs FSAL has no other resources to release on the per-FSAL
 * level.
 */

MODULE_FINI void finish(void)
{
	LogDebug(COMPONENT_FSAL,
		 "Idfs module finishing.");

	if (unregister_fsal(&IdfsFSM.fsal) != 0) {
		LogCrit(COMPONENT_FSAL,
			"Unable to unload Idfs FSAL.  Dying with extreme prejudice.");
		exit(2);
	}
}
